import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import 'package:google_fonts/google_fonts.dart';
import '../utils/app_colors.dart';
import '../widgets/glass_card.dart';

class TestApiScreen extends StatefulWidget {
  const TestApiScreen({Key? key}) : super(key: key);

  @override
  State<TestApiScreen> createState() => _TestApiScreenState();
}

class _TestApiScreenState extends State<TestApiScreen> {
  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  bool _isLoading = false;
  String _result = '';
  
  final String baseUrl = 'http://192.168.31.200';

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: isDark ? AppColors.darkGradient : AppColors.primaryGradient,
        ),
        child: Safe<PERSON>rea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                const SizedBox(height: 40),
                
                Text(
                  'اختبار API',
                  style: GoogleFonts.alexandria(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                
                const SizedBox(height: 32),
                
                GlassCard(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Text(
                        'اختبار الاتصال',
                        style: GoogleFonts.alexandria(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                      
                      const SizedBox(height: 16),
                      
                      ElevatedButton(
                        onPressed: _isLoading ? null : _testConnection,
                        child: Text('اختبار الاتصال الأساسي'),
                      ),
                      
                      const SizedBox(height: 8),
                      
                      ElevatedButton(
                        onPressed: _isLoading ? null : _testApiEndpoint,
                        child: Text('اختبار API Endpoint'),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 16),
                
                GlassCard(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Text(
                        'اختبار تسجيل الدخول',
                        style: GoogleFonts.alexandria(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                      
                      const SizedBox(height: 16),
                      
                      TextField(
                        controller: _usernameController,
                        decoration: InputDecoration(
                          labelText: 'اسم المستخدم',
                          filled: true,
                          fillColor: Colors.white.withOpacity(0.9),
                        ),
                      ),
                      
                      const SizedBox(height: 12),
                      
                      TextField(
                        controller: _passwordController,
                        obscureText: true,
                        decoration: InputDecoration(
                          labelText: 'كلمة المرور',
                          filled: true,
                          fillColor: Colors.white.withOpacity(0.9),
                        ),
                      ),
                      
                      const SizedBox(height: 16),
                      
                      ElevatedButton(
                        onPressed: _isLoading ? null : _testLogin,
                        child: _isLoading 
                            ? CircularProgressIndicator(color: Colors.white)
                            : Text('اختبار تسجيل الدخول'),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 16),
                
                if (_result.isNotEmpty)
                  GlassCard(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'النتيجة:',
                          style: GoogleFonts.alexandria(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.3),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            _result,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.white,
                              fontFamily: 'monospace',
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _testConnection() async {
    setState(() {
      _isLoading = true;
      _result = 'جاري اختبار الاتصال...';
    });

    try {
      final dio = Dio();
      final response = await dio.get(baseUrl);
      setState(() {
        _result = 'نجح الاتصال!\nStatus: ${response.statusCode}\nHeaders: ${response.headers}';
      });
    } catch (e) {
      setState(() {
        _result = 'فشل الاتصال:\n$e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testApiEndpoint() async {
    setState(() {
      _isLoading = true;
      _result = 'جاري اختبار API endpoint...';
    });

    try {
      final dio = Dio();
      final response = await dio.get('$baseUrl/user/api/index.php/api');
      setState(() {
        _result = 'نجح الوصول لـ API!\nStatus: ${response.statusCode}\nData: ${response.data}';
      });
    } catch (e) {
      setState(() {
        _result = 'فشل الوصول لـ API:\n$e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testLogin() async {
    if (_usernameController.text.isEmpty || _passwordController.text.isEmpty) {
      setState(() {
        _result = 'يرجى إدخال اسم المستخدم وكلمة المرور';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _result = 'جاري اختبار تسجيل الدخول...';
    });

    try {
      final dio = Dio();
      
      // Test different login endpoints
      final endpoints = [
        '/user/api/index.php/api/auth/login',
        '/user/api/auth/login',
        '/api/auth/login',
        '/auth/login',
      ];
      
      for (String endpoint in endpoints) {
        try {
          final response = await dio.post(
            '$baseUrl$endpoint',
            data: {
              'username': _usernameController.text,
              'password': _passwordController.text,
            },
          );
          
          setState(() {
            _result = 'نجح تسجيل الدخول مع endpoint: $endpoint\n'
                     'Status: ${response.statusCode}\n'
                     'Data: ${response.data}';
          });
          return;
        } catch (e) {
          print('Failed endpoint $endpoint: $e');
        }
      }
      
      setState(() {
        _result = 'فشل تسجيل الدخول مع جميع endpoints المختبرة';
      });
      
    } catch (e) {
      setState(() {
        _result = 'خطأ في تسجيل الدخول:\n$e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }
}
