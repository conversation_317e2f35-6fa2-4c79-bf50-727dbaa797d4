{"info": {"_postman_id": "9935d6f2-0f91-495d-86e4-a842e7970ee9", "name": "SAS4 for developers", "description": "SASv4 is a complete billing system which offers a variety of different features to suit any ISP's needs also ISP's who want power and flexibility to meet the needs of their changing technical environment and growing user base, enabling ISP managers to take full control over their precious resources and network elements.\n\nSASv4 is an AAA server is a server program that handles user requests to access computer resources, and for an enterprise, this server provides authentication, authorization, and accounting (AAA) services. The AAA server typically interacts with network access and gateway servers and with databases and directories containing user information.\n\nSASv4 API specifications allow ISP to develop API endpoints that can then be accessed by API users (e.g., third-party developers) to build mobile and web applications for their customers.\n\n---\n\n# Authentication\n\nAuthentication to the SASv4 API is performed via JWT Bearer Authentication. Every endpoint requires authentication, so you will need to add the following header to each request:\n\n`Authorization: Bearer`\n\n---\n\n# POST Request Encryption\n\nSAS4 requires encryption for all `POST` request. The encryption method is AES and can be found in many libraries such as [CryptoJS](https://code.google.com/archive/p/crypto-js/) which is used in SAS4. The POST payload has be encapsulated into a single parameter called 'payload'. The 'payload' param will hold the actual encrypted payload. See the example of our SAS4 connector which is implemented in PHP on [SAS4-connector](https://github.com/hasanenalbana/sasconnector-php) the following expmale show this process through Node.js (you can check this [script on GitHub](https://github.com/abdo0/sas4-request-encrypt-or-decrypt) for further info in NodeJs) :\n\n``` (node)\nconst request = require('request');\nconst CryptoJS = require(\"crypto-js\");\nconst form = {user: 'admin', password: 'snonosystems'};\nconst cypData = CryptoJS.AES.encrypt(JSON.stringify(form), 'abcdefghijuklmno0123456789012345');\nconst options = {\n    url: 'http://demo4.sasradius.com/admin/api/index.php/api/login',\n    json: true,\n    body: {\n        payload: cypData.toString();\n    }\n};\nrequest.post(options, (err, res, body) => {\n    if (err) {\n        return console.log(err);\n    }\n    console.log(`Status: ${res.statusCode}`);\n    console.log(body);\n});\n\n ```\n\nthe obove code will generate encryption text of the `form` that will produce the following code :\n\n```\n{ \n   payload:   'U2FsdGVkX19TGvGsXp3eR8h/fVKdKbm4tzbrEmk7s0xsrhjKy6VybasH8b7xu6xYDcvDjNk6ulb/dVoq2J417uRhJozvbPuSfkSaXD/f644=' \n}\n\n ```\n\n---\n\n# Data Types\n\nAll of the Open Banking Nigeria API responses returned are in JSON format, with these data types defined below:\n\n| Type | Description |\n| --- | --- |\n| string | A UTF-8 encoded string |\n| number | An integer |\n| datetime | An ISO8601 encoded DateTime. All datetimes are returned in UTC with offset +00:00 |\n| decimal | All monetary values are returned with up to two decimal places and may be positive (20.78) or negative (-32.50) |\n\n---\n\n# Pagination\n\nFor section which include lists that provide several records, the response may be paged depending on the total number of records that the server can return at a time. This means that to retrieve the full set of items for a given resource you may be required to make several requests. For more info you can check [Laravel Pagination](https://laravel.com/docs/6.x/pagination#introduction)\n\n## URL Parameters\n\n| Parameters | Description |\n| --- | --- |\n| page | `number` The page number you wish to retrieve |\n| count | `number` The number of items to return in a request |\n| sortBy | `string` A field name data will be sorted by |\n| diraction | `string` Sort direction (asc/desc) |\n| columns | `array` (optional) column names to retrieve |\n\n## Response\n\n| Field | Type |\n| --- | --- |\n| current_page | `number` |\n| data | `array` |\n| first_page_url | `string` |\n| from | `number` |\n| last_page | `number` |\n| last_page_url | `string` |\n| next_page_url | `string` |\n| path | `string` |\n| per_page | `number` |\n| prev_page_url | `string` |\n| to | `number` |\n| total | `number` |\n\n## Navigating through pages\n\n- If you are on the first page, the \"prev_page_url\" link will not be present in the response.\n    \n- If you are at the final page, the \"next_page_url\" link will not be present in the response\n    \n- If there are no pages and all data is returned neither \"prev_page_url\" or \"next_page_url\" links will be present in the response\n    \n\n---\n\n# Errors\n\nErrors in SAS4 API are expressed as a combination of HTTP status codes and an accompanying JSON body providing required detail where possible. You should be able to rely on the HTTP status code alone to determine the cause of the problem.\n\n## Error Response Fields\n\n| Field | Type | Description |\n| --- | --- | --- |\n| message | `string` | A human-readable message as to the specifics of the problem. For example, it may contain a detail description of what caused the problem |\n| status | `number` | The HTTP status code used in the response |\n\n## Error Response codes\n\n| Status | Code | Description |\n| --- | --- | --- |\n| 200 | \\-1 | Error |", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "13026186"}, "item": [{"name": "Authorization", "item": [{"name": "Access Granted Client Credentials", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "username", "value": "admin", "type": "text"}, {"key": "password", "value": "admin", "type": "text"}]}, "url": {"raw": "http://demo4.sasradius.com/admin/api/index.php/api/login", "protocol": "http", "host": ["demo4", "<PERSON><PERSON><PERSON><PERSON>", "com"], "path": ["admin", "api", "index.php", "api", "login"]}, "description": "To request an access token you need to send a POST request encrypted with CryptoJS for the following body parameters to the authorization server:\n\n- \"payload\" it must be encrypt the following parameters in JSON format:\n    - `user` manager username.\n    - `password` manager password.\n    - `language` site language\n\ndecrypted payload JSON use in example : `{\"username\":\"admin\",\"password\":\"snonosystems\",\"language\":\"en\"}`"}, "response": [{"name": "Access Granted Client Credentials", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "username", "value": "admin", "type": "text"}, {"key": "password", "value": "admin", "type": "text"}]}, "url": {"raw": "http://demo4.sasradius.com/admin/api/index.php/api/login", "protocol": "http", "host": ["demo4", "<PERSON><PERSON><PERSON><PERSON>", "com"], "path": ["admin", "api", "index.php", "api", "login"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Tu<PERSON>, 31 Aug 2021 10:33:18 GMT"}, {"key": "Server", "value": "Apache/2.4.29 (Ubuntu)"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PATCH, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Origin, Content-Type, X-Auth-Token , Authorization , content-type, x-xsrf-token"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "1500"}, {"key": "X-RateLimit-Remaining", "value": "1488"}, {"key": "Content-Length", "value": "381"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"status\": 200,\n    \"token\": \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOlwvXC9kZW1vNC5zYXNyYWRpdXMuY29tXC9hZG1pblwvYXBpXC9pbmRleC5waHBcL2FwaVwvbG9naW4iLCJpYXQiOjE2MzA0MDU5OTgsImV4cCI6MTYzMDQwOTU5OCwibmJmIjoxNjMwNDA1OTk4LCJqdGkiOiJCZmdvN00zN2pkbGtRRzFhIiwic3ViIjoxLCJwcnYiOiJkNzk3N2M0N2U5MTY5NjUxMDEwNzM0ZDJmYmY4Y2MxMzlmM2U1MDM0In0.7tNWgF6psOPKpPC9-zU_hEK_GLx3-BeFlIW9LE4wzYo\"\n}"}]}, {"name": "Get Token Information", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "Bearer {{access_token}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": []}, "url": {"raw": "http://{{IP-or-Domain}}/admin/api/index.php/api/auth", "protocol": "http", "host": ["{{IP-or-Domain}}"], "path": ["admin", "api", "index.php", "api", "auth"]}, "description": "Retrieves information about an existing token. The token is passed as Request Header parameter, for example:\n\nauthorization: Bearer {{access_token}}"}, "response": [{"name": "Get Token Information", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded", "name": "Content-Type", "type": "text"}], "url": {"raw": "http://demo4.sasradius.com/admin/api/index.php/api/auth", "protocol": "http", "host": ["demo4", "<PERSON><PERSON><PERSON><PERSON>", "com"], "path": ["admin", "api", "index.php", "api", "auth"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Wed, 15 Jul 2020 09:13:59 GMT"}, {"key": "Server", "value": "Apache/2.4.29 (Ubuntu)"}, {"key": "Vary", "value": "Authorization"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PATCH, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Origin, Content-Type, X-Auth-Token , Authorization , content-type, x-xsrf-token"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "500"}, {"key": "X-RateLimit-Remaining", "value": "484"}, {"key": "Content-Length", "value": "2783"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"status\": 200,\n    \"client\": {\n        \"id\": 1,\n        \"username\": \"admin\",\n        \"enabled\": 1,\n        \"city\": \"Baghdad\",\n        \"country\": \"Iraq\",\n        \"firstname\": \"Administrator\",\n        \"lastname\": \"<PERSON><PERSON><PERSON>\",\n        \"email\": \"<EMAIL>\",\n        \"phone\": null,\n        \"company\": null,\n        \"address\": null,\n        \"balance\": \"0.000\",\n        \"debt_limit\": null,\n        \"subscriber_suffix\": null,\n        \"subscriber_prefix\": null,\n        \"notes\": null,\n        \"manager_id\": 1,\n        \"mobile_auth_secret\": null,\n        \"max_users\": null,\n        \"mikrotik_addresslist\": null,\n        \"created_at\": \"2017-07-26 09:54:49\",\n        \"updated_at\": \"2019-07-25 08:55:46\",\n        \"deleted_at\": null,\n        \"acl_group_id\": 1,\n        \"site_id\": null,\n        \"avatar\": null,\n        \"parent_id\": 0,\n        \"created_by\": 0,\n        \"reward_points\": 0,\n        \"discount_rate\": \"0.00\",\n        \"avatar_data\": \"\"\n    },\n    \"permissions\": [\n        \"prm_any\",\n        \"prm_backup_create\",\n        \"prm_backup_delete\",\n        \"prm_backup_download\",\n        \"prm_backup_restore\",\n        \"prm_backup_upload\",\n        \"prm_cards_change_owner\",\n        \"prm_cards_delete\",\n        \"prm_cards_designer\",\n        \"prm_cards_download\",\n        \"prm_cards_generate_refill\",\n        \"prm_cards_generate_user\",\n        \"prm_cards_index\",\n        \"prm_cards_job_cancel\",\n        \"prm_cards_list\",\n        \"prm_cards_suspend_release\",\n        \"prm_cards_verify\",\n        \"prm_dashboard_manager\",\n        \"prm_widget_factory\",\n        \"prm_managers_change_self_password\",\n        \"prm_managers_create\",\n        \"prm_managers_delete\",\n        \"prm_managers_deposit\",\n        \"prm_managers_index\",\n        \"prm_managers_invoice\",\n        \"prm_managers_journal\",\n        \"prm_managers_login_as\",\n        \"prm_managers_receipt\",\n        \"prm_managers_sysadmin\",\n        \"prm_managers_update\",\n        \"prm_managers_withdrawal\",\n        \"prm_managers_export\",\n        \"prm_managers_rename\",\n        \"prm_nas_create\",\n        \"prm_nas_delete\",\n        \"prm_nas_index\",\n        \"prm_nas_update\",\n        \"prm_nas_export\",\n        \"prm_profiles_create\",\n        \"prm_profiles_delete\",\n        \"prm_profiles_index\",\n        \"prm_profiles_pricing\",\n        \"prm_profiles_update\",\n        \"prm_profiles_policy_manager\",\n        \"prm_report_managers_invoices\",\n        \"prm_report_managers_journal\",\n        \"prm_report_syslog\",\n        \"prm_report_user_auth_log\",\n        \"prm_report_activations\",\n        \"prm_settings\",\n        \"prm_sites_management\",\n        \"prm_users_activate\",\n        \"prm_users_activate_card\",\n        \"prm_users_activate_credit\",\n        \"prm_users_activate_user_balance\",\n        \"prm_users_advanced\",\n        \"prm_users_cancel_profile_change\",\n        \"prm_users_change_profile\",\n        \"prm_users_create\",\n        \"prm_users_delete\",\n        \"prm_users_deposit\",\n        \"prm_users_disconnect\",\n        \"prm_users_history\",\n        \"prm_users_index\",\n        \"prm_users_index_all\",\n        \"prm_users_invoice\",\n        \"prm_users_journal\",\n        \"prm_users_mac_lock\",\n        \"prm_users_ping\",\n        \"prm_users_rename\",\n        \"prm_users_reset_quota\",\n        \"prm_users_sessions_index\",\n        \"prm_users_update\",\n        \"prm_users_withdrawal\",\n        \"prm_users_export\",\n        \"prm_users_extend\",\n        \"prm_tools_announcements\",\n        \"prm_tools_import\",\n        \"prm_billing\",\n        \"prm_ucp_activate\",\n        \"prm_ucp_auto_login\",\n        \"prm_ucp_billing\",\n        \"prm_ucp_browse_packages\",\n        \"prm_ucp_change_info\",\n        \"prm_ucp_change_password\",\n        \"prm_ucp_change_profile\",\n        \"prm_ucp_data_usage\",\n        \"prm_ucp_extend\",\n        \"prm_ucp_login\",\n        \"prm_ucp_sessions\",\n        \"prm_ucp_support\",\n        \"prm_report_sessions\",\n        \"prm_report_users\",\n        \"prm_any\"\n    ],\n    \"features\": [\n        \"sastrack\",\n        \"freezone\"\n    ],\n    \"license_status\": \"1\",\n    \"license_expiration\": \"2021-06-02 09:57:16\"\n}"}]}], "description": "JWT uses access tokens for accessing APIs. A token represents a permission granted to a client to access some protected resources. The method to acquire a token is called grant.\n\nThere are different types of JWT grants. SAS4 for Developers uses the Client Credentials Grant.", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}, {"name": "User Portal (user panel)", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "payload", "value": "<text encrypted by CryptoJS>", "type": "text"}]}, "url": {"raw": "http://{{IP-or-Domain}}/user/api/index.php/api/auth/login", "protocol": "http", "host": ["{{IP-or-Domain}}"], "path": ["user", "api", "index.php", "api", "auth", "login"], "query": [{"key": "", "value": "", "disabled": true}]}, "description": "Authenticates through SAS4 and returns cookies/token \n\nencoded params:\n```json\n{\"username\":\"Example\",\"password\":\"Example\",\"language\":\"en\"}\n```"}, "response": [{"name": "Login Example", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "payload", "value": "<text encrypted by CryptoJS>", "type": "text"}], "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://{{IP_OR_}}/user/api/index.php/api/auth/login", "protocol": "http", "host": ["{{IP_OR_}}"], "path": ["user", "api", "index.php", "api", "auth", "login"], "query": [{"key": "", "value": null, "disabled": true}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 24 Sep 2020 11:06:44 GMT"}, {"key": "Server", "value": "Apache/2.4.29 (Ubuntu)"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PATCH, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Origin, Content-Type, X-Auth-Token , Authorization , content-type, x-xsrf-token"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Content-Length", "value": "382"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"status\": 200,\n    \"token\": \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************************.DdiI_fUdzbipKHuOy3vOVZUzEkEC1di8KIUxvGKt9OQ\"\n}"}]}, {"name": "Register", "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "payload", "value": "<text encrypted by CryptoJS>", "type": "text"}]}, "url": {"raw": "http://{{IP-or-Domain}}/user/api/index.php/api/register", "protocol": "http", "host": ["{{IP-or-Domain}}"], "path": ["user", "api", "index.php", "api", "register"]}, "description": "Registers a user and returns 200 on success\n\n\n```json\n{\n\"username\":\"Example\",\n\"email\":\"<EMAIL>\"\n,\"mobile\":\"07813370000\"\n,\"password\":\"Example\",\n\"confirm_password\":\"Example\",\n\"firstname\":\"Example\",\n\"lastname\":\"Example\"\n}\n```"}, "response": [{"name": "Register Example", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "payload", "value": "<text encrypted by CryptoJS>", "type": "text"}], "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://{{IP_OR_WEBSITE}}:{{PORT}}/user/api/index.php/api/register", "protocol": "http", "host": ["{{IP_OR_WEBSITE}}"], "port": "{{PORT}}", "path": ["user", "api", "index.php", "api", "register"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 24 Sep 2020 10:48:34 GMT"}, {"key": "Server", "value": "Apache/2.4.29 (Ubuntu)"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PATCH, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Origin, Content-Type, X-Auth-Token , Authorization , content-type, x-xsrf-token"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Content-Length", "value": "407"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"status\": 200,\n    \"message\": \"rsp_save_success\",\n    \"token\": \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************************.72IEQ1VKHN0F2RZyrurTHyNxXm8K8s8zxiIlCUQxtA4\"\n}"}]}, {"name": "invoices", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************************.0ZE8Fh8fV_hZ86IYgioBAA6JePlTmM_1Mqf3OtKHXIc", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "payload", "value": "<text encrypted by CryptoJS>", "type": "text"}]}, "url": {"raw": "http://{{IP-or-Domain}}/user/api/index.php/api/index/invoice", "protocol": "http", "host": ["{{IP-or-Domain}}"], "path": ["user", "api", "index.php", "api", "index", "invoice"]}, "description": "Get invoices from user portal\n\nbody: \n```json\n{\"page\":1,\"count\":10,\"sortBy\":\"id\",\"direction\":\"desc\"}\n```"}, "response": [{"name": "invoices Example", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "payload", "value": "<text encrypted by CryptoJS>", "type": "text"}]}, "url": {"raw": "http://{{IP-or-Domain}}/user/api/index.php/api/index/invoice", "protocol": "http", "host": ["{{IP-or-Domain}}"], "path": ["user", "api", "index.php", "api", "index", "invoice"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 24 Sep 2020 11:27:14 GMT"}, {"key": "Server", "value": "Apache/2.4.29 (Ubuntu)"}, {"key": "Vary", "value": "Authorization"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PATCH, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Origin, Content-Type, X-Auth-Token , Authorization , content-type, x-xsrf-token"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Content-Length", "value": "589"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"current_page\": 1,\n    \"data\": [\n        {\n            \"id\": 2,\n            \"invoice_number\": \"2020-1-2\",\n            \"type\": \"custom\",\n            \"amount\": \"0.00\",\n            \"description\": null,\n            \"paid\": 0,\n            \"created_by\": 1,\n            \"created_at\": \"2020-09-24 11:21:45\",\n            \"payment_method\": null,\n            \"due_date\": \"2020-09-24 11:21:45\"\n        }\n    ],\n    \"first_page_url\": \"http://192.168.240.122/user/api/index.php/api/index/invoice?page=1\",\n    \"from\": 1,\n    \"last_page\": 1,\n    \"last_page_url\": \"http://192.168.240.122/user/api/index.php/api/index/invoice?page=1\",\n    \"next_page_url\": null,\n    \"path\": \"http://192.168.240.122/user/api/index.php/api/index/invoice\",\n    \"per_page\": 10,\n    \"prev_page_url\": null,\n    \"to\": 1,\n    \"total\": 1\n}"}]}, {"name": "Change Subscription", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************************.0S2shL4Z1En6vE5MilTygIqxpB9PQJuwurvJjVgAb6E", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "payload", "value": "<text encrypted by CryptoJS>", "type": "text"}]}, "url": {"raw": "http://{{IP-or-Domain}}/user/api/index.php/api/service", "protocol": "http", "host": ["{{IP-or-Domain}}"], "path": ["user", "api", "index.php", "api", "service"]}, "description": "Changes subscription of the user \n\nencoded param:\n```json\n{\"new_service\":\"2\",\"current_password\":true}\n```\n\nNote: If the subscription is changed to the same subscription id, the response will be empty with a 200 Request code"}, "response": [{"name": "Change Subscription Example", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "payload", "value": "<text encrypted by CryptoJS>", "type": "text"}]}, "url": {"raw": "http://{{IP-or-Domain}}/user/api/index.php/api/service", "protocol": "http", "host": ["{{IP-or-Domain}}"], "path": ["user", "api", "index.php", "api", "service"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Sat, 26 Sep 2020 12:31:48 GMT"}, {"key": "Server", "value": "Apache/2.4.29 (Ubuntu)"}, {"key": "Vary", "value": "Authorization"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PATCH, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Origin, Content-Type, X-Auth-Token , Authorization , content-type, x-xsrf-token"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Content-Length", "value": "53"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"status\": 200,\n    \"message\": \"rsp_service_change_success\"\n}"}]}, {"name": "User sessions", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************************.PW_DxK55NTsMhmqpDx8yqqCHgrPwTOsadheJg_Y2tNc", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "payload", "value": "<text encrypted by CryptoJS>", "type": "text"}]}, "url": {"raw": "http://{{IP-or-Domain}}/user/api/index.php/api/index/session", "protocol": "http", "host": ["{{IP-or-Domain}}"], "path": ["user", "api", "index.php", "api", "index", "session"]}, "description": "Returns a list of user active sessions \n\nencoded payload: \n```json\n{\"page\":1,\"count\":10,\"sortBy\":\"radacctid\",\"direction\":\"desc\"}\n```"}, "response": [{"name": "User sessions Example", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "payload", "value": "<text encrypted by CryptoJS>", "type": "text"}]}, "url": {"raw": "http://{{IP-or-Domain}}/user/api/index.php/api/index/session", "protocol": "http", "host": ["{{IP-or-Domain}}"], "path": ["user", "api", "index.php", "api", "index", "session"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Sat, 26 Sep 2020 10:48:07 GMT"}, {"key": "Server", "value": "Apache/2.4.29 (Ubuntu)"}, {"key": "Vary", "value": "Authorization"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PATCH, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Origin, Content-Type, X-Auth-Token , Authorization , content-type, x-xsrf-token"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Content-Length", "value": "394"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"current_page\": 1,\n    \"data\": [],\n    \"first_page_url\": \"http://192.168.240.122/user/api/index.php/api/index/session?page=1\",\n    \"from\": null,\n    \"last_page\": 1,\n    \"last_page_url\": \"http://192.168.240.122/user/api/index.php/api/index/session?page=1\",\n    \"next_page_url\": null,\n    \"path\": \"http://192.168.240.122/user/api/index.php/api/index/session\",\n    \"per_page\": 10,\n    \"prev_page_url\": null,\n    \"to\": null,\n    \"total\": 0\n}"}]}, {"name": "User traffic", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************************.PW_DxK55NTsMhmqpDx8yqqCHgrPwTOsadheJg_Y2tNc", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "payload", "value": "<text encrypted by CryptoJS>", "type": "text"}]}, "url": {"raw": "http://{{IP-or-Domain}}/user/api/index.php/api/traffic", "protocol": "http", "host": ["{{IP-or-Domain}}"], "path": ["user", "api", "index.php", "api", "traffic"]}, "description": "Returns user traffic over the last 30 days, each day traffic is given in rx,tx with 30 values, each value for each day. \n\nencoded params:\n```json\n {\"report_type\":\"daily\",\"month\":9,\"year\":2020,\"user_id\":null}\n```"}, "response": [{"name": "User traffic example", "originalRequest": {"method": "POST", "header": [{"key": "payload", "value": "U2FsdGVkX18xH4wh1gewbYvA5CuHq0FB0UbN6BWp7xYbIz67k1l+we0Md/Fx0SB9NORDsuzk9fRhrPxWjgOT9V2ceY9WW8cRHbXvVfmv73M=", "type": "text", "disabled": true}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "payload", "value": "<text encrypted by CryptoJS>", "type": "text"}]}, "url": {"raw": "http://{{IP-or-Domain}}/user/api/index.php/api/traffic", "protocol": "http", "host": ["{{IP-or-Domain}}"], "path": ["user", "api", "index.php", "api", "traffic"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Sat, 26 Sep 2020 10:39:48 GMT"}, {"key": "Server", "value": "Apache/2.4.29 (Ubuntu)"}, {"key": "Vary", "value": "Authorization"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PATCH, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Origin, Content-Type, X-Auth-Token , Authorization , content-type, x-xsrf-token"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Content-Length", "value": "389"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"status\": 200,\n    \"data\": {\n        \"rx\": [\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0\n        ],\n        \"tx\": [\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0\n        ],\n        \"total\": [\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0\n        ],\n        \"total_real\": [\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0\n        ],\n        \"free_traffic\": [\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0,\n            0\n        ]\n    }\n}"}]}, {"name": "Redeem code ", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************************.PW_DxK55NTsMhmqpDx8yqqCHgrPwTOsadheJg_Y2tNc", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "payload", "value": "<text encrypted by CryptoJS>", "type": "text"}]}, "url": {"raw": "http://{{IP-or-Domain}}/user/api/index.php/api/redeem", "protocol": "http", "host": ["{{IP-or-Domain}}"], "path": ["user", "api", "index.php", "api", "redeem"]}, "description": "Get the available subscriptions from SAS4\n\nencoded parameters: \n```json\n{\"pin\":\"136154\"}\n```"}, "response": [{"name": "Redeem code Example", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "payload", "value": "<text encrypted by CryptoJS>", "type": "text"}]}, "url": {"raw": "http://{{IP-or-Domain}}/user/api/index.php/api/redeem", "protocol": "http", "host": ["{{IP-or-Domain}}"], "path": ["user", "api", "index.php", "api", "redeem"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Sat, 26 Sep 2020 11:09:48 GMT"}, {"key": "Server", "value": "Apache/2.4.29 (Ubuntu)"}, {"key": "Vary", "value": "Authorization"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PATCH, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Origin, Content-Type, X-Auth-Token , Authorization , content-type, x-xsrf-token"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Content-Length", "value": "38"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"status\": 200,\n    \"message\": \"rsp_success\"\n}"}]}, {"name": "Activate user subscription", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************************.PW_DxK55NTsMhmqpDx8yqqCHgrPwTOsadheJg_Y2tNc", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "payload", "value": "<text encrypted by CryptoJS>", "type": "text"}]}, "url": {"raw": "http://{{IP-or-Domain}}/user/api/index.php/api/user/activate ", "protocol": "http", "host": ["{{IP-or-Domain}}"], "path": ["user", "api", "index.php", "api", "user", "activate "]}, "description": "Activate user subscription and deduct from balance\n\nencoded params:\n```json\n{\"uuid\":\"edad62f6-d324-7510-8c6a-90a9b4a3fee2\",\"current_password\":true}\n```\n\nThe `uuid` is a rate-limit mechanism generated from client-side using uuid/guid functions at the load time of client-side interface so that if a user clicks activate multiple times it would all be sent using\nthe same uuid and thus wouldn't activate multiple times."}, "response": [{"name": "Activate user subscription Example", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "payload", "value": "<text encrypted by CryptoJS>", "type": "text"}]}, "url": {"raw": "http://{{IP-or-Domain}}/user/api/index.php/api/redeem", "protocol": "http", "host": ["{{IP-or-Domain}}"], "path": ["user", "api", "index.php", "api", "redeem"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Sat, 26 Sep 2020 11:09:48 GMT"}, {"key": "Server", "value": "Apache/2.4.29 (Ubuntu)"}, {"key": "Vary", "value": "Authorization"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PATCH, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Origin, Content-Type, X-Auth-Token , Authorization , content-type, x-xsrf-token"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Content-Length", "value": "38"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"status\": 200,\n    \"message\": \"rsp_success\"\n}"}]}, {"name": "Activate Subscription Extension", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************************.0S2shL4Z1En6vE5MilTygIqxpB9PQJuwurvJjVgAb6E", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "payload", "value": "<text encrypted by CryptoJS>", "type": "text"}, {"key": "", "value": "", "type": "text", "disabled": true}]}, "url": {"raw": "http://{{IP-or-Domain}}/user/api/index.php/api/user/extend", "protocol": "http", "host": ["{{IP-or-Domain}}"], "path": ["user", "api", "index.php", "api", "user", "extend"]}, "description": "Adds an extension to the user extension from the available extensions and returns 200 on success\n\nencoded params:\n```json\n{\"profile_id\":\"3\",\"current_password\":true}\n```"}, "response": [{"name": "Activate Subscription Extension Example", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "payload", "value": "<text encrypted by CryptoJS>", "type": "text"}, {"key": "", "value": "", "type": "text", "disabled": true}]}, "url": {"raw": "http://{{IP-or-Domain}}/user/api/index.php/api/user/extend", "protocol": "http", "host": ["{{IP-or-Domain}}"], "path": ["user", "api", "index.php", "api", "user", "extend"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Sat, 26 Sep 2020 12:50:27 GMT"}, {"key": "Server", "value": "Apache/2.4.29 (Ubuntu)"}, {"key": "Vary", "value": "Authorization"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PATCH, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Origin, Content-Type, X-Auth-Token , Authorization , content-type, x-xsrf-token"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Content-Length", "value": "38"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"status\": 200,\n    \"message\": \"rsp_success\"\n}"}]}, {"name": "Language keywords", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************************.0ZE8Fh8fV_hZ86IYgioBAA6JePlTmM_1Mqf3OtKHXIc", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://{{IP-or-Domain}}/user/api/index.php/api/resources/language/ar", "protocol": "http", "host": ["{{IP-or-Domain}}"], "path": ["user", "api", "index.php", "api", "resources", "language", "ar"]}}, "response": [{"name": "Language keywords en", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://{{IP-or-Domain}}/user/api/index.php/api/resources/language/en", "protocol": "http", "host": ["{{IP-or-Domain}}"], "path": ["user", "api", "index.php", "api", "resources", "language", "en"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "html", "header": [{"key": "Date", "value": "Thu, 24 Sep 2020 11:44:29 GMT"}, {"key": "Server", "value": "Apache/2.4.29 (Ubuntu)"}, {"key": "Vary", "value": "Authorization,Accept-Encoding"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PATCH, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Origin, Content-Type, X-Auth-Token , Authorization , content-type, x-xsrf-token"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Content-Encoding", "value": "gzip"}, {"key": "Content-Length", "value": "1644"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Content-Type", "value": "text/html; charset=UTF-8"}], "cookie": [], "body": "{\n    \"info\": {\n        \"id\": \"en\",\n        \"name\": \"English\",\n        \"direction\": \"ltr\",\n        \"author\": \"<EMAIL>\",\n        \"font\": \"Arial\"\n    },\n    \"words\": {\n        \"menu_account\": \"Account\",\n        \"menu_billing\": \"Billing\",\n        \"menu_invoices\": \"Invoices\",\n        \"menu_payments\": \"Payments\",\n        \"menu_data_usage\": \"Data Usage\",\n        \"menu_sessions\": \"Sessions\",\n        \"menu_packages\": \"Packages\",\n        \"menu_support\": \"Support\",\n        \"menu_journal\": \"Journal\",\n        \"menu_documents\": \"Documents\",\n\n        \"global_table_actions\": \"Actions\",\n        \"global_label_download\": \"Download\",\n        \"global_label_upload\": \"Upload\",\n        \"global_label_rename\": \"Rename\",\n        \"global_label_delete\": \"Delete\",\n\n\n        \"header_user_portal\": \"User Portal\",\n\n        \"user_document_table_name\": \"Document Name\",\n        \"user_document_table_size\": \"Size\",\n        \"user_document_table_date\": \"Date\",\n\n        \"session_table_started\": \"Started On\",\n        \"session_table_ended\": \"Ended On\",\n        \"session_table_ip\": \"IP\",\n        \"session_table_download\": \"Download\",\n        \"session_table_upload\": \"Upload\",\n        \"session_table_mac\": \"MAC\",\n        \"session_table_profile\": \"Profile\",\n\n        \"invoices_table_no\": \"Invoice No\",\n        \"invoices_table_date\": \"Date\",\n        \"invoices_table_amount\": \"Amount\",\n        \"invoices_table_description\": \"Description\",\n        \"invoices_table_due_date\": \"Due Date\",\n        \"invoices_table_is_paid\": \"Paid\",\n        \"invoices_table_paid\": \"Paid\",\n        \"invoices_table_unpaid\": \"Unpaid\",\n\n        \"payment_table_no\": \"Receipt No\",\n        \"payment_table_date\": \"Date\",\n        \"payment_table_type\": \"Type\",\n        \"payment_table_amount\": \"Amount\",\n        \"payment_table_description\": \"Description\",\n        \"payment_table_created_by\": \"Created By\",\n\n        \"user_journal_table_date\": \"Date\",\n        \"user_journal_table_cr\": \"CR\",\n        \"user_journal_table_dr\": \"DR\",\n        \"user_journal_table_amount\": \"Amount\",\n        \"user_journal_table_balance\": \"Balance\",\n        \"user_journal_table_operation\": \"Operation\",\n        \"user_journal_table_description\": \"Description\",\n\n\n        \"header_logout\": \"Logout\",\n        \"breadcrumb_account\": \"Account Information\",\n        \"breadcrumb_invoices\": \"Invoices\",\n        \"breadcrumb_sessions\": \"Login Sessions\",\n        \"breadcrumb_journal\": \"Balance Journal\",\n        \"breadcrumb_payments\": \"Payments\",\n        \"breadcrumb_usage\": \"Data Usage\",\n        \"breadcrumb_packages\": \"Available Packages\",\n        \"breadcrumb_activate_renew\": \"Purchase Service\",\n        \"breadcrumb_change_service\": \"Change Service\",\n        \"breadcrumb_activate_extend\": \"Extend Service\",\n        \"breadcrumb_support_tickets\": \"Support Tickets\",\n        \"breadcrumb_user_documents\": \"User Documents\",\n\n\n        \"account_label_days\": \"day(s)\",\n        \"account_label_remaining_days\": \"Remaining days\",\n        \"account_label_remaining_traffic\": \"Remaining Traffic\",\n        \"account_label_balance\": \"Account Balance\",\n        \"account_label_unpaid_invoices\": \"Unpaid Invoices\",\n        \"account_label_remaining_uptime\": \"Remaining Uptime\",\n        \"account_label_customer_information\": \"Customer Information\",\n        \"account_label_id\": \"ID\",\n        \"account_label_name\": \"Customer Name\",\n        \"account_label_username\": \"Username\",\n        \"account_label_email\": \"Email\",\n        \"account_label_phone\": \"Phone\",\n        \"account_label_address\": \"Address\",\n        \"account_label_company\": \"Company\",\n        \"account_label_created_on\": \"Registered On\",\n\n        \"account_label_redeem\": \"Redeem Code\",\n        \"account_label_redeem_card_number\": \"Enter Card Number\",\n\n        \"account_label_service_info\": \"Service Information\",\n        \"account_label_current_service\": \"Current Service\",\n        \"account_label_service_description\": \"Service Description\",\n        \"account_label_service_subscription\": \"Subscription\",\n        \"account_label_expiration\": \"Expiration\",\n        \"account_label_status\": \"Status\",\n        \"account_label_service_price\": \"Service Price\",\n        \"account_label_traffic_usage\": \"Remaining Traffic\",\n        \"account_label_traffic_dl\": \"Remaining Traffic (Download)\",\n        \"account_label_traffic_ul\": \"Remaining Traffic (Upload)\",\n        \"account_label_static_ip\": \"Static IP\",\n        \"account_label_auto_renew\": \"Auto Renew\",\n        \"account_label_prompt_new_password\" : \"New Password\",\n        \"account_prompt_current_password\": \"Current Password\",\n\n        \"account_label_loan\": \"You have a loan of \",\n        \"account_label_deduction\": \"will be deducted from your account on next activation.\",\n\n        \"account_action_extend\": \"Extend Service\",\n        \"account_action_activate\": \"Activate Account\",\n        \"account_action_change_service\": \"Change Service\",\n        \"account_action_change_password\": \"Change Password\",\n\n        \"activate_label_service\": \"Active Service\",\n        \"activate_label_description\": \"Service Description\",\n        \"activate_label_subscription\": \"Subscription\",\n        \"activate_label_expiration\": \"Expiration\",\n        \"activate_label_price\": \"Service Price\",\n        \"activate_label_balance\": \"Balance\",\n        \"activate_label_use_balance\": \"Use Available Balance\",\n        \"activate_label_insufficient_balance\": \"Insufficient Balance\",\n        \"activate_action_activate\": \"Activate\",\n        \"activate_label_payment_method\": \"Choose Payment Method\",\n        \"activate_label_no_method\": \"No Payment Method Available\",\n        \"activate_label_service_activate\": \"Service Activated\",\n        \"activate_label_unknown_error\": \"Unknown Error Occurred\",\n        \"activate_label_contact_support\": \"Please report this error to support department\",\n        \"activate_label_already_active\": \"Your service is already active\",\n\n        \"extend_label_select_extension\": \"Select Service Extension\",\n        \"extend_label_days\": \"day(s)\",\n        \"extend_label_months\": \"month(s)\",\n        \"extend_label_traffic\": \"of Data Traffic\",\n        \"extend_label_traffic_dl\": \"of Download Traffic\",\n        \"extend_label_traffic_ul\": \"of Upload Traffic\",\n        \"extend_label_minutes\": \"minutes(s)\",\n        \"extend_label_hours\": \"hour(s)\",\n        \"extend_label_purchase\": \"Purchase\",\n        \"extend_label_service_extended\": \"Service Extended\",\n        \"extend_label_unknown_error\": \"Unknown Error Occurred\",\n        \"extend_label_contact_support\": \"Please report this error to support department\",\n\n        \"packages_no_description\": \"No description available\",\n\n        \"activation_confirmation_message\": \"Activate Your Account ?\",\n\n        \"rsp_service_change_success\": \"Service changed successfully\",\n        \"rsp_service_change_invalid_service\": \"Invalid service selected !\",\n        \"rsp_service_change_user_active\": \"User already active\",\n        \"rsp_error\": \"Error occurred\",\n        \"rsp_user_exists\" : \"User exists, try another username\",\n\n        \"msg_invalid_current_password\": \"Incorrect password\"\n    }\n}\n"}, {"name": "Language keywords ar", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://{{IP-or-Domain}}/user/api/index.php/api/resources/language/ar", "protocol": "http", "host": ["{{IP-or-Domain}}"], "path": ["user", "api", "index.php", "api", "resources", "language", "ar"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "html", "header": [{"key": "Date", "value": "Thu, 24 Sep 2020 11:45:13 GMT"}, {"key": "Server", "value": "Apache/2.4.29 (Ubuntu)"}, {"key": "Vary", "value": "Authorization,Accept-Encoding"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PATCH, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Origin, Content-Type, X-Auth-Token , Authorization , content-type, x-xsrf-token"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "58"}, {"key": "Content-Encoding", "value": "gzip"}, {"key": "Content-Length", "value": "1958"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Content-Type", "value": "text/html; charset=UTF-8"}], "cookie": [], "body": "{\n    \"info\": {\n        \"id\": \"ar\",\n        \"name\": \"Arabic\",\n        \"direction\": \"ltr\",\n        \"author\": \"<EMAIL>\",\n        \"font\": \"Arial\"\n    },\n    \"words\": {\n        \"menu_account\": \"معلومات عامة\",\n        \"menu_billing\": \"الحسابات\",\n        \"menu_invoices\": \"الفواتير\",\n        \"menu_payments\": \"الايصالات\",\n        \"menu_data_usage\": \"بيانات الاستهلاك\",\n        \"menu_sessions\": \"الجلسات\",\n        \"menu_packages\": \"الباقات\",\n        \"menu_support\": \"الدعم الفني\",\n        \"menu_journal\": \"سجل الحسابات\",\n        \"menu_documents\": \"وثائق شخصية\",\n\n        \"global_table_actions\": \"العمليات\",\n        \"global_label_download\": \"تحميل\",\n        \"global_label_upload\": \"رفع\",\n        \"global_label_delete\": \"حذف\",\n\n\n        \"header_user_portal\": \"صفحة العميل\",\n\n        \"user_document_table_name\": \"اسم الوثيقة\",\n        \"user_document_table_size\": \"الحجم\",\n        \"user_document_table_date\": \"تأريخ الرفع\",\n\n\n        \"session_table_started\": \"وقت البدئ\",\n        \"session_table_ended\": \"وقت النهاية\",\n        \"session_table_ip\": \"IP\",\n        \"session_table_download\": \"التحميل\",\n        \"session_table_upload\": \"الرفع\",\n        \"session_table_mac\": \"MAC\",\n        \"session_table_profile\": \"الخدمة\",\n\n        \"invoices_table_no\": \"رقم الفاتورة\",\n        \"invoices_table_date\": \"تأريخ\",\n        \"invoices_table_amount\": \"المبلغ\",\n        \"invoices_table_description\": \"تفاصيل\",\n        \"invoices_table_due_date\": \"تأريخ الاستحقاق\",\n        \"invoices_table_is_paid\": \"مدفوعة\",\n        \"invoices_table_paid\": \"مدفوعة\",\n        \"invoices_table_unpaid\": \"غير مدفوعة\",\n\n        \"payment_table_no\": \"رقم الايصال\",\n        \"payment_table_date\": \"التأريخ\",\n        \"payment_table_type\": \"النوع\",\n        \"payment_table_amount\": \"المبلغ\",\n        \"payment_table_description\": \"تفاصيل\",\n        \"payment_table_created_by\": \"اصدر من قبل\",\n\n        \"user_journal_table_date\": \"التأريخ\",\n        \"user_journal_table_cr\": \"دائن\",\n        \"user_journal_table_dr\": \"مدين\",\n        \"user_journal_table_amount\": \"المبلغ\",\n        \"user_journal_table_balance\": \"الرصيد\",\n        \"user_journal_table_operation\": \"العملية\",\n        \"user_journal_table_description\": \"تفاصيل\",\n\n\n        \"header_logout\": \"خروج\",\n        \"breadcrumb_account\": \"معلومات الحساب\",\n        \"breadcrumb_invoices\": \"الفواتير\",\n        \"breadcrumb_sessions\": \"جلسات الدخول\",\n        \"breadcrumb_journal\": \"سجل الحسابات\",\n        \"breadcrumb_payments\": \"الايصالات\",\n        \"breadcrumb_usage\": \"استهلاك البيانات\",\n        \"breadcrumb_packages\": \"الباقات المتوفرة\",\n        \"breadcrumb_activate_renew\": \"شراء باقة\",\n        \"breadcrumb_change_service\": \"تغيير الخدمة\",\n        \"breadcrumb_activate_extend\": \"تمديد الخدمة\",\n        \"breadcrumb_user_documents\": \"الوثائق الشخصية\",\n\n        \"account_label_days\": \"يوم\",\n        \"account_label_remaining_days\": \"الايام المتبقية\",\n        \"account_label_remaining_traffic\": \"البيانات المتبقية\",\n        \"account_label_balance\": \"رصيد الحساب\",\n        \"account_label_unpaid_invoices\": \"الفواتير المستحقة\",\n        \"account_label_remaining_uptime\": \"الوقت المتبقي\",\n        \"account_label_customer_information\": \"معلومات العميل\",\n        \"account_label_id\": \"الرمز\",\n        \"account_label_name\": \"الاسم\",\n        \"account_label_username\": \"اسم الدخول\",\n        \"account_label_email\": \"البريد الالكتروني\",\n        \"account_label_phone\": \"الهاتف\",\n        \"account_label_address\": \"العنوان\",\n        \"account_label_company\": \"الشركة\",\n        \"account_label_created_on\": \"سجل بتأريخ\",\n\n        \"account_label_redeem\": \"تعبئة بطاقة\",\n        \"account_label_redeem_card_number\": \"ادخل رقم البطاقة\",\n\n        \"account_label_service_info\": \"معلومات الاشتراك\",\n        \"account_label_current_service\": \"الخدمة الحالية\",\n        \"account_label_service_description\": \"تفاصيل الخدمة\",\n        \"account_label_service_subscription\": \"حالة الاشتراك\",\n        \"account_label_expiration\": \"تأريخ الانتهاء\",\n        \"account_label_status\": \"الحالة\",\n        \"account_label_service_price\": \"سعر الخدمة\",\n        \"account_label_traffic_usage\": \"البيانات المتبقية\",\n        \"account_label_traffic_dl\": \"البيانات المتبقية - التحميل\",\n        \"account_label_traffic_ul\": \"البيانات المتبقية - الرفع\",\n        \"account_label_static_ip\": \"عنوان انترنت ثابت\",\n        \"account_label_auto_renew\": \"تجديد تلقائي\",\n        \"account_label_prompt_new_password\" : \"كلمة السر الجديدة\",\n        \"account_prompt_current_password\": \"كلمة السر الحالية\",\n\n        \"account_action_extend\": \"تمديد الخدمة\",\n        \"account_action_activate\": \"تفعيل الحساب\",\n        \"account_action_change_service\": \"تغيير الخدمة\",\n        \"account_action_change_password\": \"تغيير كلمة السر\",\n\n        \"activate_label_service\": \"الخدمة الفعال\",\n        \"activate_label_description\": \"تفاصيل الخدمة\",\n        \"activate_label_subscription\": \"حالة الاشتراك\",\n        \"activate_label_expiration\": \"تأريخ الانتهاء\",\n        \"activate_label_price\": \"سعر الخدمة\",\n        \"activate_label_balance\": \"الرصيد المتوفر\",\n        \"activate_label_use_balance\": \"استخدام الراصيد المتوفر\",\n        \"activate_label_insufficient_balance\": \"الرصيد غير كافي\",\n        \"activate_action_activate\": \"تفعيل\",\n        \"activate_label_payment_method\": \"اختر وسيلة الدفع\",\n        \"activate_label_no_method\": \"وسائل الدفع غير متوفرة\",\n        \"activate_label_service_activate\": \"تم تفعيل الخدمة\",\n        \"activate_label_unknown_error\": \"حصل خطأ غير متوقع\",\n        \"activate_label_contact_support\": \"الرجاء تبليغ قسم الدعم الفني بالمشكلة\",\n        \"activate_label_already_active\": \"الاشتراك مازال فعال\",\n\n        \"account_label_loan\": \"لديك قرض ب  \",\n        \"account_label_deduction\": \"سيتم استقطاعه من حسابك عند التفعيل القادم.\",\n\n\n        \"extend_label_select_extension\": \"اختر باقة التمديد\",\n        \"extend_label_days\": \"يوم\",\n        \"extend_label_months\": \"شهر\",\n        \"extend_label_traffic\": \"من البيانات\",\n        \"extend_label_traffic_dl\": \"من بيانات التحميل\",\n        \"extend_label_traffic_ul\": \"من بيانات الرفع\",\n        \"extend_label_minutes\": \"دقيقة\",\n        \"extend_label_hours\": \"ساعة\",\n        \"extend_label_purchase\": \"شراء\",\n        \"extend_label_service_extended\": \"تم تمديد الخدمة\",\n        \"extend_label_unknown_error\": \"حصل خطأ غير متوقع\",\n        \"extend_label_contact_support\": \"الرجاء تبليغ قسم الدعم الفني بالمشكلة\",\n\n        \"packages_no_description\": \"لا يوجد تفاصيل لهذه الباقة\",\n\n        \"activation_confirmation_message\": \"هل تود تفعيل حسابك ؟\",\n\n\n        \"rsp_service_change_success\": \"تم تغيير الباقة\",\n        \"rsp_service_change_invalid_service\": \"الباقة المختارة غير صحيحة\",\n        \"rsp_service_change_user_active\": \"الاشتراك مازال فعال\",\n        \"rsp_error\": \"حصل خطأ في النظام\",\n        \"msg_invalid_current_password\": \"كلمة السر القديمة غير صحيحة\"\n\n    }\n}\n"}]}, {"name": "User Details & Permissions", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************************.0ZE8Fh8fV_hZ86IYgioBAA6JePlTmM_1Mqf3OtKHXIc", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://{{IP-or-Domain}}/user/api/index.php/api/user", "protocol": "http", "host": ["{{IP-or-Domain}}"], "path": ["user", "api", "index.php", "api", "user"]}}, "response": [{"name": "user", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded", "name": "Content-Type", "type": "text"}], "url": {"raw": "http://{{IP-or-Domain}}/user/api/index.php/api/user", "protocol": "http", "host": ["{{IP-or-Domain}}"], "path": ["user", "api", "index.php", "api", "user"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 24 Sep 2020 11:37:15 GMT"}, {"key": "Server", "value": "Apache/2.4.29 (Ubuntu)"}, {"key": "Vary", "value": "Authorization"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PATCH, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Origin, Content-Type, X-Auth-Token , Authorization , content-type, x-xsrf-token"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Content-Length", "value": "601"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"status\": 200,\n    \"data\": {\n        \"username\": \"Example1\",\n        \"firstname\": null,\n        \"lastname\": null,\n        \"name\": null,\n        \"email\": \"<EMAIL>\",\n        \"phone\": \"07813370000\",\n        \"address\": null,\n        \"company\": null,\n        \"registered_on\": {\n            \"date\": \"2020-09-24 13:48:34.000000\",\n            \"timezone_type\": 3,\n            \"timezone\": \"Europe/Moscow\"\n        },\n        \"id\": 6,\n        \"static_ip\": null,\n        \"balance\": 0,\n        \"auto_renew\": 0,\n        \"profile_id\": 1\n    },\n    \"permissions\": [\n        \"prm_ucp_activate\",\n        \"prm_ucp_auto_login\",\n        \"prm_ucp_billing\",\n        \"prm_ucp_browse_packages\",\n        \"prm_ucp_change_info\",\n        \"prm_ucp_change_password\",\n        \"prm_ucp_change_profile\",\n        \"prm_ucp_data_usage\",\n        \"prm_ucp_extend\",\n        \"prm_ucp_login\",\n        \"prm_ucp_sessions\",\n        \"prm_ucp_support\"\n    ]\n}"}]}, {"name": "Balance Info", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************************.0ZE8Fh8fV_hZ86IYgioBAA6JePlTmM_1Mqf3OtKHXIc", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://{{IP-or-Domain}}/user/api/index.php/api/dashboard", "protocol": "http", "host": ["{{IP-or-Domain}}"], "path": ["user", "api", "index.php", "api", "dashboard"]}}, "response": [{"name": "Balance Info Request example", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://{{IP-or-Domain}}/user/api/index.php/api/dashboard", "protocol": "http", "host": ["{{IP-or-Domain}}"], "path": ["user", "api", "index.php", "api", "dashboard"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 24 Sep 2020 11:47:52 GMT"}, {"key": "Server", "value": "Apache/2.4.29 (Ubuntu)"}, {"key": "Vary", "value": "Authorization"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PATCH, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Origin, Content-Type, X-Auth-Token , Authorization , content-type, x-xsrf-token"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Content-Length", "value": "188"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"status\": 200,\n    \"data\": {\n        \"remaining_days\": 0,\n        \"remaining_traffic\": \"_\",\n        \"remaining_uptime\": \"_\",\n        \"balance\": \"0.00\",\n        \"unpaid_invoices\": 1,\n        \"loan\": {\n            \"rx_mb\": null,\n            \"tx_mb\": null,\n            \"rxtx_mb\": null,\n            \"days\": null\n        }\n    }\n}"}]}, {"name": "Profiles / Services", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************************.0ZE8Fh8fV_hZ86IYgioBAA6JePlTmM_1Mqf3OtKHXIc", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://{{IP-or-Domain}}/user/api/index.php/api/service", "protocol": "http", "host": ["{{IP-or-Domain}}"], "path": ["user", "api", "index.php", "api", "service"]}}, "response": [{"name": "Profiles response Example", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://{{IP-or-Domain}}/user/api/index.php/api/service", "protocol": "http", "host": ["{{IP-or-Domain}}"], "path": ["user", "api", "index.php", "api", "service"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 24 Sep 2020 11:51:14 GMT"}, {"key": "Server", "value": "Apache/2.4.29 (Ubuntu)"}, {"key": "Vary", "value": "Authorization"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PATCH, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Origin, Content-Type, X-Auth-Token , Authorization , content-type, x-xsrf-token"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Content-Length", "value": "326"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"status\": 200,\n    \"data\": {\n        \"profile_name\": \"default-2Mbit-1Month\",\n        \"description\": null,\n        \"expiration\": \"2020-09-24 13:48:34\",\n        \"profile_id\": 1,\n        \"status\": false,\n        \"price\": 20,\n        \"subscription_status\": {\n            \"status\": false,\n            \"traffic\": true,\n            \"expiration\": false,\n            \"uptime\": true\n        },\n        \"limits\": {\n            \"rx_bytes\": null,\n            \"tx_bytes\": null,\n            \"rxtx_bytes\": null,\n            \"uptime_seconds\": null\n        }\n    }\n}"}]}, {"name": "Packages ", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************************.PW_DxK55NTsMhmqpDx8yqqCHgrPwTOsadheJg_Y2tNc", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://{{IP-or-Domain}}/user/api/index.php/api/packages", "protocol": "http", "host": ["{{IP-or-Domain}}"], "path": ["user", "api", "index.php", "api", "packages"]}, "description": "Get the available subscriptions"}, "response": [{"name": "Packages ", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://{{IP-Or-Domain}}/user/api/index.php/api/packages", "protocol": "http", "host": ["{{IP-Or-Domain}}"], "path": ["user", "api", "index.php", "api", "packages"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Sat, 26 Sep 2020 10:42:56 GMT"}, {"key": "Server", "value": "Apache/2.4.29 (Ubuntu)"}, {"key": "Vary", "value": "Authorization"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PATCH, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Origin, Content-Type, X-Auth-Token , Authorization , content-type, x-xsrf-token"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Content-Length", "value": "147"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"status\": 200,\n    \"data\": [\n        {\n            \"id\": 1,\n            \"name\": \"default-2Mbit-1Month\",\n            \"description\": null,\n            \"price\": 20\n        },\n        {\n            \"id\": 2,\n            \"name\": \"band_1\",\n            \"description\": null,\n            \"price\": 10\n        }\n    ]\n}"}]}, {"name": "Menus", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************************.0S2shL4Z1En6vE5MilTygIqxpB9PQJuwurvJjVgAb6E", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://{{IP-or-Domain}}/user/api/index.php/api/resources/menu", "protocol": "http", "host": ["{{IP-or-Domain}}"], "path": ["user", "api", "index.php", "api", "resources", "menu"]}, "description": "Returns menus and their respective links for web-based UIs"}, "response": [{"name": "Menus Example", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://{{IP-or-Domain}}/user/api/index.php/api/resources/menu", "protocol": "http", "host": ["{{IP-or-Domain}}"], "path": ["user", "api", "index.php", "api", "resources", "menu"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Sat, 26 Sep 2020 11:53:40 GMT"}, {"key": "Server", "value": "Apache/2.4.29 (Ubuntu)"}, {"key": "Vary", "value": "Authorization"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PATCH, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Origin, Content-Type, X-Auth-Token , Authorization , content-type, x-xsrf-token"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Content-Length", "value": "1244"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[\n    {\n        \"name\": \"account\",\n        \"title\": \"menu_account\",\n        \"icon\": \"fas fa-user\",\n        \"link\": \"/account\",\n        \"weight\": 0,\n        \"parent\": \"root\",\n        \"acl\": \"any\"\n    },\n    {\n        \"name\": \"billing\",\n        \"title\": \"menu_billing\",\n        \"icon\": \"fas fa-book-open\",\n        \"link\": \"billing\",\n        \"weight\": 0,\n        \"parent\": \"root\",\n        \"acl\": \"prm_ucp_billing\"\n    },\n    {\n        \"name\": \"invoices\",\n        \"title\": \"menu_invoices\",\n        \"icon\": \"fas fa-none\",\n        \"link\": \"/invoices\",\n        \"weight\": 0,\n        \"parent\": \"billing\",\n        \"acl\": \"prm_ucp_billing\"\n    },\n    {\n        \"name\": \"payments\",\n        \"title\": \"menu_payments\",\n        \"icon\": \"fas fa-none\",\n        \"link\": \"/payments\",\n        \"weight\": 0,\n        \"parent\": \"billing\",\n        \"acl\": \"prm_ucp_billing\"\n    },\n    {\n        \"name\": \"journal\",\n        \"title\": \"menu_journal\",\n        \"icon\": \"fas fa-none\",\n        \"link\": \"/journal\",\n        \"weight\": 0,\n        \"parent\": \"billing\",\n        \"acl\": \"prm_ucp_billing\"\n    },\n    {\n        \"name\": \"date_usage\",\n        \"title\": \"menu_data_usage\",\n        \"icon\": \"fas fa-chart-area\",\n        \"link\": \"/data\",\n        \"weight\": 4,\n        \"parent\": \"root\",\n        \"acl\": \"prm_ucp_data_usage\"\n    },\n    {\n        \"name\": \"sessions\",\n        \"title\": \"menu_sessions\",\n        \"icon\": \"fas fa-list\",\n        \"link\": \"/sessions\",\n        \"weight\": 4,\n        \"parent\": \"root\",\n        \"acl\": \"prm_ucp_sessions\"\n    },\n    {\n        \"name\": \"packages\",\n        \"title\": \"menu_packages\",\n        \"icon\": \"fas fa-puzzle-piece\",\n        \"link\": \"/packages\",\n        \"weight\": 5,\n        \"parent\": \"root\",\n        \"acl\": \"prm_ucp_browse_packages\"\n    },\n    {\n        \"name\": \"support\",\n        \"title\": \"menu_support\",\n        \"icon\": \"fas fa-life-ring\",\n        \"link\": \"/support\",\n        \"weight\": 9,\n        \"parent\": \"root\",\n        \"acl\": \"prm_ucp_support\"\n    }\n]"}]}, {"name": "Get Extensions", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************************.0S2shL4Z1En6vE5MilTygIqxpB9PQJuwurvJjVgAb6E", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://{{IP-or-Domain}}/user/api/index.php/api/extensions/2", "protocol": "http", "host": ["{{IP-or-Domain}}"], "path": ["user", "api", "index.php", "api", "extensions", "2"]}, "description": "Returns a full list of the available subscriptions extensions"}, "response": [{"name": "Get Extensions Example", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://{{IP-or-Domain}}/user/api/index.php/api/extensions/2", "protocol": "http", "host": ["{{IP-or-Domain}}"], "path": ["user", "api", "index.php", "api", "extensions", "2"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Sat, 26 Sep 2020 12:47:52 GMT"}, {"key": "Server", "value": "Apache/2.4.29 (Ubuntu)"}, {"key": "Vary", "value": "Authorization"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PATCH, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Origin, Content-Type, X-Auth-Token , Authorization , content-type, x-xsrf-token"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Content-Length", "value": "114"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"status\": 200,\n    \"data\": [\n        {\n            \"name\": \"extension test\",\n            \"id\": 3,\n            \"price\": 10\n        },\n        {\n            \"name\": \"Extension test 2\",\n            \"id\": 4,\n            \"price\": 100\n        }\n    ]\n}"}]}], "description": "/user/api/index.php/api/"}, {"name": "Profiles", "item": [{"name": "Profiles - Simple List", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOjEsImlzcyI6Imh0dHA6Ly9kZW1vNC5zYXNyYWRpdXMuY29tL2FkbWluL2FwaS9pbmRleC5waHAvYXBpL2xvZ2luIiwiaWF0IjoxNTk2MDA2OTc3LCJleHAiOjE1OTYwMjg1NzcsIm5iZiI6MTU5NjAwNjk3NywianRpIjoiSTFqNklvbUFRRzI0aGpkNCJ9.HRDe_f7UiOcU93eLc2STKZdEf4SE2R-28hzCRsP69Ws", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://demo4.sasradius.com/admin/api/index.php/api/list/profile/0", "protocol": "http", "host": ["demo4", "<PERSON><PERSON><PERSON><PERSON>", "com"], "path": ["admin", "api", "index.php", "api", "list", "profile", "0"]}, "description": "Retrieve data necessary for activation"}, "response": [{"name": "Profiles - Simple List", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://demo4.sasradius.com/admin/api/index.php/api/list/profile/0", "protocol": "http", "host": ["demo4", "<PERSON><PERSON><PERSON><PERSON>", "com"], "path": ["admin", "api", "index.php", "api", "list", "profile", "0"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Wed, 29 Jul 2020 08:35:39 GMT"}, {"key": "Server", "value": "Apache/2.4.29 (Ubuntu)"}, {"key": "Vary", "value": "Authorization"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PATCH, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Origin, Content-Type, X-Auth-Token , Authorization , content-type, x-xsrf-token"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "500"}, {"key": "X-RateLimit-Remaining", "value": "497"}, {"key": "Content-Length", "value": "134"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"0\": 200,\n    \"status\": 200,\n    \"data\": [\n        {\n            \"id\": 1,\n            \"name\": \"default-2Mbit-1Month\"\n        },\n        {\n            \"id\": 4,\n            \"name\": \"10Mbit-1Month\"\n        },\n        {\n            \"id\": 5,\n            \"name\": \"20Mbit-1Month\"\n        }\n    ]\n}"}]}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}