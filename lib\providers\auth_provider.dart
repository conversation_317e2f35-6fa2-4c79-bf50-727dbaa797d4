import 'package:flutter/material.dart';
import '../services/api_service.dart';
import '../models/user_model.dart';

class AuthProvider with ChangeNotifier {
  ApiService _apiService = ApiService('http://192.168.31.200');

  bool _isLoading = false;
  String? _error;
  UserModel? _currentUser;

  // Getters
  bool get isAuthenticated => _apiService.isAuthenticated;
  bool get isLoading => _isLoading;
  String? get error => _error;
  UserModel? get currentUser => _currentUser;
  String? get token => _apiService.currentToken;

  // Auto login on app start
  Future<bool> tryAutoLogin() async {
    _setLoading(true);
    try {
      final success = await _apiService.tryAutoLogin();
      if (success) {
        _currentUser = _apiService.currentUser;
        _clearError();
      }
      return success;
    } catch (e) {
      _setError('فشل في تسجيل الدخول التلقائي');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Login
  Future<bool> loginUser(String username, String password, String baseUrl) async {
    _setLoading(true);
    _clearError();

    try {
      // Initialize API service with base URL if needed
      if (_apiService.baseUrl != baseUrl) {
        _apiService = ApiService(baseUrl);
      }

      final success = await _apiService.loginUser(username, password);

      if (success) {
        _currentUser = _apiService.currentUser;
        _clearError();
        notifyListeners();
        return true;
      } else {
        _setError(_apiService.error ?? 'فشل تسجيل الدخول');
        return false;
      }
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Logout
  Future<void> logout() async {
    _setLoading(true);
    try {
      await _apiService.logout();
      _currentUser = null;
      _clearError();
    } catch (e) {
      _setError('فشل في تسجيل الخروج');
    } finally {
      _setLoading(false);
    }
  }

  // Refresh user data
  Future<void> refreshUserData() async {
    if (!isAuthenticated) return;

    _setLoading(true);
    try {
      _currentUser = await _apiService.refreshUserData();
      _clearError();
    } catch (e) {
      _setError('فشل في تحديث البيانات');
    } finally {
      _setLoading(false);
    }
  }

  // Get API service for other providers
  ApiService get apiService => _apiService;

  // Private methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }
}