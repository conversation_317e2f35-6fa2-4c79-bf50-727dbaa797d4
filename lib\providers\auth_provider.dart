import 'package:flutter/material.dart';
import 'package:internet_billing_app/api/user_api.dart';
import 'package:internet_billing_app/api/admin_api.dart';

class AuthProvider with ChangeNotifier {
  bool _isAuthenticated = false;
  String? _token;
  String? _role; // 'user' or 'admin'

  bool get isAuthenticated => _isAuthenticated;
  String? get token => _token;
  String? get role => _role;

  Future<void> loginUser(String username, String password, String baseUrl) async {
    final api = UserApi(baseUrl);
    final response = await api.loginUser(username, password);
    _token = response['token'];
    _role = 'user';
    _isAuthenticated = true;
    notifyListeners();
  }

  Future<void> loginAdmin(String username, String password, String baseUrl) async {
    final api = AdminApi(baseUrl);
    final response = await api.loginAdmin(username, password);
    _token = response['token'];
    _role = 'admin';
    _isAuthenticated = true;
    notifyListeners();
  }

  void logout() {
    _isAuthenticated = false;
    _token = null;
    _role = null;
    notifyListeners();
  }
}