import 'dart:convert';
import 'package:http/http.dart' as http;
import '../encryption/aes_service.dart';

class AdminApi {
  final String baseUrl;
  AdminApi(this.baseUrl);

  Future<Map<String, dynamic>> loginAdmin(String username, String password) async {
    final encrypted = AesService().encryptData({
      'username': username,
      'password': password
    });
    final response = await http.post(
      Uri.parse('\$baseUrl/login'),
      body: {'payload': encrypted},
    );
    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('فشل تسجيل دخول المشرف');
    }
  }

  Future<Map<String, dynamic>> getTokenInfo(String token) async {
    final response = await http.get(
      Uri.parse('\$baseUrl/auth'),
      headers: {'Authorization': 'Bearer $token'},
    );
    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('فشل استرداد معلومات التوكن');
    }
  }
}