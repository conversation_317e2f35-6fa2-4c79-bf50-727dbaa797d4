import 'package:flutter/material.dart';

class UserPackagesScreen extends StatelessWidget {
  const UserPackagesScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Placeholder data
    final packages = [
      {'name': 'باقات 10 جيجا', 'price': 50},
      {'name': 'باقات 50 جيجا', 'price': 200},
      {'name': 'باقات غير محدودة', 'price': 500},
    ];
    return Scaffold(
      appBar: AppBar(
        title: const Text('الباقات'),
      ),
      body: ListView.builder(
        itemCount: packages.length,
        itemBuilder: (context, index) {
          final pkg = packages[index];
          return ListTile(
            leading: const Icon(Icons.wifi),
            title: Text(pkg['name'] as String),
            subtitle: Text('السعر: ${pkg['price'].toString()} ريال'),
            trailing: ElevatedButton(
              onPressed: () {
                // TODO: Implement package change logic
              },
              child: const Text('تغيير'),
            ),
          );
        },
      ),
    );
  }
}