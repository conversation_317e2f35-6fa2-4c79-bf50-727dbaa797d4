import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../widgets/glass_card.dart';
import '../../widgets/custom_button.dart' as custom;
import '../../utils/app_colors.dart';

class UserPackagesScreen extends StatefulWidget {
  const UserPackagesScreen({Key? key}) : super(key: key);

  @override
  State<UserPackagesScreen> createState() => _UserPackagesScreenState();
}

class _UserPackagesScreenState extends State<UserPackagesScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  int selectedPackageIndex = 1; // Current package index

  final List<Map<String, dynamic>> packages = [
    {
      'name': 'باقة الألياف البرونزية',
      'speed': '50 ميجا',
      'data': 'غير محدود',
      'price': 99.00,
      'features': [
        'سرعة تحميل 50 ميجا',
        'سرعة رفع 25 ميجا',
        'بيانات غير محدودة',
        'دعم فني 24/7',
        'راوتر مجاني',
      ],
      'color': AppColors.accentOrange,
      'popular': false,
    },
    {
      'name': 'باقة الألياف الذهبية',
      'speed': '100 ميجا',
      'data': 'غير محدود',
      'price': 149.00,
      'features': [
        'سرعة تحميل 100 ميجا',
        'سرعة رفع 50 ميجا',
        'بيانات غير محدودة',
        'دعم فني 24/7',
        'راوتر مجاني',
        'IP ثابت',
      ],
      'color': AppColors.accentGreen,
      'popular': true,
    },
    {
      'name': 'باقة الألياف البلاتينية',
      'speed': '200 ميجا',
      'data': 'غير محدود',
      'price': 199.00,
      'features': [
        'سرعة تحميل 200 ميجا',
        'سرعة رفع 100 ميجا',
        'بيانات غير محدودة',
        'دعم فني 24/7',
        'راوتر مجاني',
        'IP ثابت',
        'أولوية في الدعم',
      ],
      'color': AppColors.accentPurple,
      'popular': false,
    },
  ];

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
    _fadeController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: isDark ? AppColors.darkGradient : AppColors.primaryGradient,
        ),
        child: SafeArea(
          child: CustomScrollView(
            physics: const BouncingScrollPhysics(),
            slivers: [
              // Custom App Bar
              SliverAppBar(
                expandedHeight: 120,
                floating: false,
                pinned: true,
                backgroundColor: Colors.transparent,
                elevation: 0,
                leading: IconButton(
                  icon: const Icon(Icons.arrow_back, color: Colors.white),
                  onPressed: () {
                    HapticFeedback.lightImpact();
                    Navigator.pop(context);
                  },
                ),
                flexibleSpace: FlexibleSpaceBar(
                  title: Text(
                    'الباقات المتاحة',
                    style: GoogleFonts.alexandria(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  centerTitle: true,
                ),
              ),

              // Packages Content
              SliverPadding(
                padding: const EdgeInsets.all(16),
                sliver: SliverList(
                  delegate: SliverChildListDelegate([
                    FadeTransition(
                      opacity: _fadeAnimation,
                      child: AnimationLimiter(
                        child: Column(
                          children: [
                            // Header Info
                            GlassCard(
                              child: Column(
                                children: [
                                  const Icon(
                                    Icons.wifi,
                                    size: 48,
                                    color: Colors.white,
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    'اختر الباقة المناسبة لك',
                                    style: GoogleFonts.alexandria(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'جميع الباقات تشمل إنترنت ألياف ضوئية عالي السرعة',
                                    style: GoogleFonts.alexandria(
                                      fontSize: 14,
                                      color: Colors.white.withOpacity(0.8),
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            ),

                            const SizedBox(height: 24),

                            // Packages List
                            ...packages.asMap().entries.map((entry) {
                              final index = entry.key;
                              final package = entry.value;
                              return AnimationConfiguration.staggeredList(
                                position: index,
                                duration: const Duration(milliseconds: 600),
                                child: SlideAnimation(
                                  verticalOffset: 50.0,
                                  child: FadeInAnimation(
                                    child: _buildPackageCard(package, index),
                                  ),
                                ),
                              );
                            }).toList(),

                            const SizedBox(height: 32),
                          ],
                        ),
                      ),
                    ),
                  ]),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPackageCard(Map<String, dynamic> package, int index) {
    final isSelected = index == selectedPackageIndex;
    final isPopular = package['popular'] as bool;
    final packageColor = package['color'] as Color;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Stack(
        children: [
          GlassCard(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Package Header
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: packageColor.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: packageColor.withOpacity(0.3),
                        ),
                      ),
                      child: Icon(
                        Icons.wifi,
                        color: packageColor,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            package['name'],
                            style: GoogleFonts.alexandria(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Text(
                                package['speed'],
                                style: GoogleFonts.alexandria(
                                  fontSize: 14,
                                  color: packageColor,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                '•',
                                style: GoogleFonts.alexandria(
                                  fontSize: 14,
                                  color: Colors.white.withOpacity(0.5),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                package['data'],
                                style: GoogleFonts.alexandria(
                                  fontSize: 14,
                                  color: Colors.white.withOpacity(0.8),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // Price
                Row(
                  children: [
                    Text(
                      '${package['price'].toStringAsFixed(0)}',
                      style: GoogleFonts.alexandria(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'ر.س',
                          style: GoogleFonts.alexandria(
                            fontSize: 16,
                            color: Colors.white.withOpacity(0.8),
                          ),
                        ),
                        Text(
                          'شهرياً',
                          style: GoogleFonts.alexandria(
                            fontSize: 12,
                            color: Colors.white.withOpacity(0.6),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // Features
                ...package['features'].map<Widget>((feature) {
                  return Container(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: Row(
                      children: [
                        Icon(
                          Icons.check_circle,
                          color: packageColor,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            feature,
                            style: GoogleFonts.alexandria(
                              fontSize: 14,
                              color: Colors.white.withOpacity(0.9),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),

                const SizedBox(height: 24),

                // Action Button
                SizedBox(
                  width: double.infinity,
                  child: custom.CustomButton(
                    text: isSelected ? 'الباقة الحالية' : 'اختيار الباقة',
                    type: isSelected
                        ? custom.ButtonType.secondary
                        : custom.ButtonType.gradient,
                    onPressed: isSelected ? null : () {
                      HapticFeedback.lightImpact();
                      setState(() {
                        selectedPackageIndex = index;
                      });
                      _showUpgradeDialog(package);
                    },
                    height: 48,
                  ),
                ),
              ],
            ),
          ),

          // Popular Badge
          if (isPopular)
            Positioned(
              top: 16,
              right: 16,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  gradient: AppColors.accentGradient,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.accentOrange.withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Text(
                  'الأكثر شعبية',
                  style: GoogleFonts.alexandria(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),
            ),

          // Current Package Badge
          if (isSelected)
            Positioned(
              top: 16,
              left: 16,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppColors.accentGreen.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: AppColors.accentGreen.withOpacity(0.5),
                  ),
                ),
                child: Text(
                  'الباقة الحالية',
                  style: GoogleFonts.alexandria(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: AppColors.accentGreen,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  void _showUpgradeDialog(Map<String, dynamic> package) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Theme.of(context).brightness == Brightness.dark
              ? AppColors.darkCard
              : Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Text(
            'تأكيد تغيير الباقة',
            style: GoogleFonts.alexandria(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Text(
            'هل تريد تغيير باقتك إلى ${package['name']}؟\n\nسيتم تطبيق التغيير في بداية الدورة القادمة.',
            style: GoogleFonts.alexandria(
              fontSize: 14,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'إلغاء',
                style: GoogleFonts.alexandria(
                  fontSize: 14,
                  color: AppColors.textSecondary,
                ),
              ),
            ),
            custom.CustomButton(
              text: 'تأكيد',
              type: custom.ButtonType.primary,
              onPressed: () {
                Navigator.of(context).pop();
                // Implement package change logic
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'تم طلب تغيير الباقة بنجاح',
                      style: GoogleFonts.alexandria(),
                    ),
                    backgroundColor: AppColors.accentGreen,
                  ),
                );
              },
              height: 40,
              fontSize: 14,
            ),
          ],
        );
      },
    );
  }
}