import 'dart:convert';

class UserModel {
  final int id;
  final String username;
  final String? email;
  final String? firstName;
  final String? lastName;
  final String? phone;
  final String? address;
  final double balance;
  final String status;
  final String? currentPackage;
  final String? currentService;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final Map<String, dynamic>? permissions;
  final Map<String, dynamic>? additionalData;

  UserModel({
    required this.id,
    required this.username,
    this.email,
    this.firstName,
    this.lastName,
    this.phone,
    this.address,
    required this.balance,
    required this.status,
    this.currentPackage,
    this.currentService,
    this.createdAt,
    this.updatedAt,
    this.permissions,
    this.additionalData,
  });

  String get fullName {
    if (firstName != null && lastName != null) {
      return '$firstName $lastName';
    } else if (firstName != null) {
      return firstName!;
    } else if (lastName != null) {
      return lastName!;
    } else {
      return username;
    }
  }

  bool get isActive => status.toLowerCase() == 'active';

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] ?? 0,
      username: json['username'] ?? '',
      email: json['email'],
      firstName: json['first_name'] ?? json['firstname'],
      lastName: json['last_name'] ?? json['lastname'],
      phone: json['phone'] ?? json['mobile'],
      address: json['address'],
      balance: double.tryParse(json['balance']?.toString() ?? '0') ?? 0.0,
      status: json['status'] ?? 'inactive',
      currentPackage: json['current_package'] ?? json['package_name'],
      currentService: json['current_service'] ?? json['service_name'],
      createdAt: json['created_at'] != null 
          ? DateTime.tryParse(json['created_at']) 
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.tryParse(json['updated_at']) 
          : null,
      permissions: json['permissions'] is Map<String, dynamic> 
          ? json['permissions'] 
          : null,
      additionalData: json['additional_data'] is Map<String, dynamic> 
          ? json['additional_data'] 
          : null,
    );
  }

  factory UserModel.fromJsonString(String jsonString) {
    return UserModel.fromJson(json.decode(jsonString));
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'email': email,
      'first_name': firstName,
      'last_name': lastName,
      'phone': phone,
      'address': address,
      'balance': balance,
      'status': status,
      'current_package': currentPackage,
      'current_service': currentService,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'permissions': permissions,
      'additional_data': additionalData,
    };
  }

  String toJsonString() {
    return json.encode(toJson());
  }

  UserModel copyWith({
    int? id,
    String? username,
    String? email,
    String? firstName,
    String? lastName,
    String? phone,
    String? address,
    double? balance,
    String? status,
    String? currentPackage,
    String? currentService,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? permissions,
    Map<String, dynamic>? additionalData,
  }) {
    return UserModel(
      id: id ?? this.id,
      username: username ?? this.username,
      email: email ?? this.email,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      balance: balance ?? this.balance,
      status: status ?? this.status,
      currentPackage: currentPackage ?? this.currentPackage,
      currentService: currentService ?? this.currentService,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      permissions: permissions ?? this.permissions,
      additionalData: additionalData ?? this.additionalData,
    );
  }

  @override
  String toString() {
    return 'UserModel(id: $id, username: $username, fullName: $fullName, balance: $balance, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.id == id && other.username == username;
  }

  @override
  int get hashCode => id.hashCode ^ username.hashCode;
}
