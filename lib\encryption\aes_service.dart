import 'dart:convert';
import 'package:encrypt/encrypt.dart';

class AesService {
  static final key = Key.fromUtf8('abcdefghijuklmno0123456789012345');
  static final iv = IV.fromLength(16);

  String encryptData(Map<String, dynamic> data) {
    final encrypter = Encrypter(AES(key));
    final encrypted = encrypter.encrypt(json.encode(data), iv: iv);
    return encrypted.base64;
  }

  Map<String, dynamic> decryptData(String encryptedData) {
    final encrypter = Encrypter(AES(key));
    final decrypted = encrypter.decrypt64(encryptedData, iv: iv);
    return json.decode(decrypted);
  }
}