import 'package:flutter/material.dart';

class UserInvoicesScreen extends StatelessWidget {
  const UserInvoicesScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Placeholder data
    final invoices = [
      {'id': 'INV001', 'amount': 100, 'status': 'مدفوع'},
      {'id': 'INV002', 'amount': 150, 'status': 'غير مدفوع'},
    ];
    return Scaffold(
      appBar: AppBar(
        title: const Text('الفواتير'),
      ),
      body: ListView.builder(
        itemCount: invoices.length,
        itemBuilder: (context, index) {
          final invoice = invoices[index];
          return ListTile(
            leading: const Icon(Icons.receipt_long),
            title: Text('فاتورة رقم: ${invoice['id']}'),
            subtitle: Text('المبلغ: ${invoice['amount']} - الحالة: ${invoice['status']}'),
            trailing: invoice['status'] == 'مدفوع'
                ? const Icon(Icons.check_circle, color: Colors.green)
                : const Icon(Icons.error, color: Colors.red),
          );
        },
      ),
    );
  }
}