import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'screens/admin/dashboard_screen.dart';
import 'screens/admin/profiles_screen.dart';
import 'screens/user/login_screen.dart';
import 'screens/user/invoices_screen.dart';
import 'screens/user/packages_screen.dart';
import 'screens/user/dashboard_screen.dart';
import 'screens/test_api_screen.dart';
import 'providers/auth_provider.dart';
import 'providers/profile_provider.dart';
import 'providers/language_provider.dart';
import 'providers/packages_provider.dart';
import 'providers/invoices_provider.dart';
import 'utils/app_theme.dart';
import 'utils/app_colors.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  runApp(const InternetBillingApp());
}

class InternetBillingApp extends StatelessWidget {
  const InternetBillingApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => ProfileProvider()),
        ChangeNotifierProvider(create: (_) => LanguageProvider()..loadLanguage('ar')),
        ChangeNotifierProxyProvider<AuthProvider, PackagesProvider>(
          create: (_) => PackagesProvider(AuthProvider().apiService),
          update: (_, auth, __) => PackagesProvider(auth.apiService),
        ),
        ChangeNotifierProxyProvider<AuthProvider, InvoicesProvider>(
          create: (_) => InvoicesProvider(AuthProvider().apiService),
          update: (_, auth, __) => InvoicesProvider(auth.apiService),
        ),
      ],
      child: Consumer<LanguageProvider>(
        builder: (context, languageProvider, child) {
          return MaterialApp(
            title: 'تطبيق إدارة الإنترنت الاحترافي',
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: ThemeMode.system,
            locale: languageProvider.locale,
            supportedLocales: const [
              Locale('ar'),
              Locale('en'),
            ],
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            builder: (context, child) {
              return Directionality(
                textDirection: languageProvider.locale.languageCode == 'ar' 
                    ? TextDirection.rtl 
                    : TextDirection.ltr,
                child: child!,
              );
            },
            initialRoute: '/',
            routes: {
              '/': (context) => const UserLoginScreen(),
              '/test': (context) => const TestApiScreen(),
              '/admin/dashboard': (context) => const AdminDashboardScreen(),
              '/admin/profiles': (context) => const AdminProfilesScreen(),
              '/user/login': (context) => const UserLoginScreen(),
              '/user/invoices': (context) => const UserInvoicesScreen(),
              '/user/packages': (context) => const UserPackagesScreen(),
              '/user/dashboard': (context) => const UserDashboardScreen(),
            },
          );
        },
      ),
    );
  }
}
