import 'dart:convert';

class PackageModel {
  final int id;
  final String name;
  final String? description;
  final double price;
  final String? currency;
  final int? downloadSpeed; // in Mbps
  final int? uploadSpeed; // in Mbps
  final int? dataLimit; // in GB, null for unlimited
  final int? validityDays;
  final bool isActive;
  final bool isPopular;
  final List<String> features;
  final Map<String, dynamic>? additionalData;

  PackageModel({
    required this.id,
    required this.name,
    this.description,
    required this.price,
    this.currency,
    this.downloadSpeed,
    this.uploadSpeed,
    this.dataLimit,
    this.validityDays,
    required this.isActive,
    this.isPopular = false,
    required this.features,
    this.additionalData,
  });

  String get speedDisplay {
    if (downloadSpeed != null && uploadSpeed != null) {
      return '${downloadSpeed}/${uploadSpeed} ميجا';
    } else if (downloadSpeed != null) {
      return '$downloadSpeed ميجا';
    } else {
      return 'غير محدد';
    }
  }

  String get dataLimitDisplay {
    if (dataLimit == null || dataLimit == 0) {
      return 'غير محدود';
    } else if (dataLimit! >= 1024) {
      return '${(dataLimit! / 1024).toStringAsFixed(1)} تيرا';
    } else {
      return '$dataLimit جيجا';
    }
  }

  String get priceDisplay {
    final currencySymbol = currency ?? 'ر.س';
    return '${price.toStringAsFixed(2)} $currencySymbol';
  }

  String get validityDisplay {
    if (validityDays == null) {
      return 'غير محدد';
    } else if (validityDays! >= 30) {
      final months = (validityDays! / 30).round();
      return months == 1 ? 'شهر واحد' : '$months أشهر';
    } else {
      return '$validityDays يوم';
    }
  }

  factory PackageModel.fromJson(Map<String, dynamic> json) {
    // Parse features from different possible formats
    List<String> parseFeatures(dynamic featuresData) {
      if (featuresData is List) {
        return featuresData.map((e) => e.toString()).toList();
      } else if (featuresData is String) {
        try {
          final decoded = jsonDecode(featuresData);
          if (decoded is List) {
            return decoded.map((e) => e.toString()).toList();
          }
        } catch (e) {
          // If JSON decode fails, split by common delimiters
          return featuresData.split(RegExp(r'[,;|\n]'))
              .map((e) => e.trim())
              .where((e) => e.isNotEmpty)
              .toList();
        }
      }
      return [];
    }

    return PackageModel(
      id: json['id'] ?? 0,
      name: json['name'] ?? json['package_name'] ?? '',
      description: json['description'] ?? json['package_description'],
      price: double.tryParse(json['price']?.toString() ?? '0') ?? 0.0,
      currency: json['currency'] ?? 'ر.س',
      downloadSpeed: int.tryParse(json['download_speed']?.toString() ?? ''),
      uploadSpeed: int.tryParse(json['upload_speed']?.toString() ?? ''),
      dataLimit: int.tryParse(json['data_limit']?.toString() ?? ''),
      validityDays: int.tryParse(json['validity_days']?.toString() ?? ''),
      isActive: json['is_active'] == true || json['status'] == 'active',
      isPopular: json['is_popular'] == true || json['popular'] == true,
      features: parseFeatures(json['features'] ?? json['package_features'] ?? []),
      additionalData: json['additional_data'] is Map<String, dynamic> 
          ? json['additional_data'] 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'currency': currency,
      'download_speed': downloadSpeed,
      'upload_speed': uploadSpeed,
      'data_limit': dataLimit,
      'validity_days': validityDays,
      'is_active': isActive,
      'is_popular': isPopular,
      'features': features,
      'additional_data': additionalData,
    };
  }

  String toJsonString() {
    return json.encode(toJson());
  }

  PackageModel copyWith({
    int? id,
    String? name,
    String? description,
    double? price,
    String? currency,
    int? downloadSpeed,
    int? uploadSpeed,
    int? dataLimit,
    int? validityDays,
    bool? isActive,
    bool? isPopular,
    List<String>? features,
    Map<String, dynamic>? additionalData,
  }) {
    return PackageModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      currency: currency ?? this.currency,
      downloadSpeed: downloadSpeed ?? this.downloadSpeed,
      uploadSpeed: uploadSpeed ?? this.uploadSpeed,
      dataLimit: dataLimit ?? this.dataLimit,
      validityDays: validityDays ?? this.validityDays,
      isActive: isActive ?? this.isActive,
      isPopular: isPopular ?? this.isPopular,
      features: features ?? this.features,
      additionalData: additionalData ?? this.additionalData,
    );
  }

  @override
  String toString() {
    return 'PackageModel(id: $id, name: $name, price: $priceDisplay, speed: $speedDisplay)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PackageModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
