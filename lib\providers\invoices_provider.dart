import 'package:flutter/material.dart';
import '../services/api_service.dart';
import '../models/invoice_model.dart';

class InvoicesProvider with ChangeNotifier {
  final ApiService _apiService;
  
  List<InvoiceModel> _invoices = [];
  bool _isLoading = false;
  String? _error;
  int _currentPage = 1;
  bool _hasMoreData = true;

  InvoicesProvider(this._apiService);

  // Getters
  List<InvoiceModel> get invoices => _invoices;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get hasMoreData => _hasMoreData;
  int get currentPage => _currentPage;

  // Get pending invoices
  List<InvoiceModel> get pendingInvoices {
    return _invoices.where((invoice) => invoice.isPending).toList();
  }

  // Get paid invoices
  List<InvoiceModel> get paidInvoices {
    return _invoices.where((invoice) => invoice.isPaid).toList();
  }

  // Get overdue invoices
  List<InvoiceModel> get overdueInvoices {
    return _invoices.where((invoice) => invoice.isOverdue).toList();
  }

  // Get total pending amount
  double get totalPendingAmount {
    return pendingInvoices.fold(0.0, (sum, invoice) => sum + invoice.remainingAmount);
  }

  // Get total paid amount
  double get totalPaidAmount {
    return paidInvoices.fold(0.0, (sum, invoice) => sum + invoice.amount);
  }

  // Load invoices (first page)
  Future<void> loadInvoices({bool refresh = false}) async {
    if (refresh) {
      _currentPage = 1;
      _hasMoreData = true;
      _invoices.clear();
    }

    _setLoading(true);
    _clearError();
    
    try {
      final newInvoices = await _apiService.getInvoices(
        page: _currentPage,
        count: 10,
      );
      
      if (refresh) {
        _invoices = newInvoices;
      } else {
        _invoices.addAll(newInvoices);
      }
      
      // Check if there's more data
      _hasMoreData = newInvoices.length >= 10;
      _currentPage++;
      
      _clearError();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Load more invoices (pagination)
  Future<void> loadMoreInvoices() async {
    if (!_hasMoreData || _isLoading) return;
    
    await loadInvoices();
  }

  // Refresh invoices
  Future<void> refreshInvoices() async {
    await loadInvoices(refresh: true);
  }

  // Get invoice by ID
  InvoiceModel? getInvoiceById(int id) {
    try {
      return _invoices.firstWhere((invoice) => invoice.id == id);
    } catch (e) {
      return null;
    }
  }

  // Get invoices by status
  List<InvoiceModel> getInvoicesByStatus(InvoiceStatus status) {
    return _invoices.where((invoice) => invoice.status == status).toList();
  }

  // Get recent invoices (last 5)
  List<InvoiceModel> get recentInvoices {
    final sortedInvoices = List<InvoiceModel>.from(_invoices);
    sortedInvoices.sort((a, b) => b.issueDate.compareTo(a.issueDate));
    return sortedInvoices.take(5).toList();
  }

  // Get invoices for current month
  List<InvoiceModel> get currentMonthInvoices {
    final now = DateTime.now();
    return _invoices.where((invoice) {
      return invoice.issueDate.year == now.year && 
             invoice.issueDate.month == now.month;
    }).toList();
  }

  // Get invoices for specific month/year
  List<InvoiceModel> getInvoicesForMonth(int year, int month) {
    return _invoices.where((invoice) {
      return invoice.issueDate.year == year && 
             invoice.issueDate.month == month;
    }).toList();
  }

  // Mark invoice as paid (local update)
  void markInvoiceAsPaid(int invoiceId, {DateTime? paidDate, double? paidAmount}) {
    final index = _invoices.indexWhere((invoice) => invoice.id == invoiceId);
    if (index != -1) {
      _invoices[index] = _invoices[index].copyWith(
        status: InvoiceStatus.paid,
        paidDate: paidDate ?? DateTime.now(),
        paidAmount: paidAmount ?? _invoices[index].amount,
      );
      notifyListeners();
    }
  }

  // Get invoice statistics
  Map<String, dynamic> get invoiceStatistics {
    return {
      'total_invoices': _invoices.length,
      'pending_invoices': pendingInvoices.length,
      'paid_invoices': paidInvoices.length,
      'overdue_invoices': overdueInvoices.length,
      'total_pending_amount': totalPendingAmount,
      'total_paid_amount': totalPaidAmount,
      'average_invoice_amount': _invoices.isNotEmpty 
          ? _invoices.fold(0.0, (sum, invoice) => sum + invoice.amount) / _invoices.length
          : 0.0,
    };
  }

  // Private methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  // Clear all data
  void clear() {
    _invoices.clear();
    _currentPage = 1;
    _hasMoreData = true;
    _clearError();
    notifyListeners();
  }
}
