import 'package:dio/dio.dart';
import '../encryption/aes_service.dart';

class UserApi {
  final String baseUrl;
  late final Dio _dio;
  String? _token;

  UserApi(this.baseUrl) {
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
      },
    ));

    // Add interceptor for logging
    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      logPrint: (obj) => print('[API] $obj'),
    ));

    // Add interceptor for token
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        if (_token != null) {
          options.headers['Authorization'] = 'Bearer $_token';
        }
        handler.next(options);
      },
    ));
  }

  void setToken(String token) {
    _token = token;
  }

  Future<Map<String, dynamic>> loginUser(String username, String password, {String language = 'ar'}) async {
    try {
      final encrypted = AesService().encryptData({
        'username': username,
        'password': password,
        'language': language
      });

      final response = await _dio.post(
        '/user/api/index.php/api/auth/login',
        data: {'payload': encrypted},
      );

      if (response.statusCode == 200 && response.data != null) {
        final data = response.data as Map<String, dynamic>;
        if (data['token'] != null) {
          setToken(data['token']);
        }
        return data;
      } else {
        throw Exception('فشل تسجيل الدخول');
      }
    } on DioException catch (e) {
      print('[API Error] Login failed: ${e.message}');
      if (e.response?.data != null) {
        print('[API Error] Response: ${e.response?.data}');
      }
      throw Exception('فشل تسجيل الدخول: ${e.message}');
    } catch (e) {
      print('[API Error] Unexpected error: $e');
      throw Exception('فشل تسجيل الدخول');
    }
  }

  Future<Map<String, dynamic>> getUserDetails() async {
    try {
      final response = await _dio.get('/user/api/index.php/api/user');

      if (response.statusCode == 200) {
        return response.data as Map<String, dynamic>;
      } else {
        throw Exception('فشل في جلب بيانات المستخدم');
      }
    } on DioException catch (e) {
      print('[API Error] Get user details failed: ${e.message}');
      throw Exception('فشل في جلب بيانات المستخدم: ${e.message}');
    }
  }

  Future<List<dynamic>> getPackages() async {
    try {
      final response = await _dio.get('/user/api/index.php/api/packages');

      if (response.statusCode == 200) {
        return response.data as List<dynamic>;
      } else {
        throw Exception('فشل في جلب الباقات');
      }
    } on DioException catch (e) {
      print('[API Error] Get packages failed: ${e.message}');
      throw Exception('فشل في جلب الباقات: ${e.message}');
    }
  }

  Future<List<dynamic>> getServices() async {
    try {
      final response = await _dio.get('/user/api/index.php/api/service');

      if (response.statusCode == 200) {
        return response.data as List<dynamic>;
      } else {
        throw Exception('فشل في جلب الخدمات');
      }
    } on DioException catch (e) {
      print('[API Error] Get services failed: ${e.message}');
      throw Exception('فشل في جلب الخدمات: ${e.message}');
    }
  }

  Future<Map<String, dynamic>> getInvoices({int page = 1, int count = 10}) async {
    try {
      final encrypted = AesService().encryptData({
        'page': page,
        'count': count,
        'sortBy': 'id',
        'direction': 'desc'
      });

      final response = await _dio.post(
        '/user/api/index.php/api/index/invoice',
        data: {'payload': encrypted},
      );

      if (response.statusCode == 200) {
        return response.data as Map<String, dynamic>;
      } else {
        throw Exception('فشل في جلب الفواتير');
      }
    } on DioException catch (e) {
      print('[API Error] Get invoices failed: ${e.message}');
      throw Exception('فشل في جلب الفواتير: ${e.message}');
    }
  }

  Future<Map<String, dynamic>> getUserSessions({int page = 1, int count = 10}) async {
    try {
      final encrypted = AesService().encryptData({
        'page': page,
        'count': count,
        'sortBy': 'radacctid',
        'direction': 'desc'
      });

      final response = await _dio.post(
        '/user/api/index.php/api/index/session',
        data: {'payload': encrypted},
      );

      if (response.statusCode == 200) {
        return response.data as Map<String, dynamic>;
      } else {
        throw Exception('فشل في جلب الجلسات');
      }
    } on DioException catch (e) {
      print('[API Error] Get sessions failed: ${e.message}');
      throw Exception('فشل في جلب الجلسات: ${e.message}');
    }
  }

  Future<Map<String, dynamic>> getTrafficData({
    String reportType = 'daily',
    int? month,
    int? year,
    int? userId,
  }) async {
    try {
      final encrypted = AesService().encryptData({
        'report_type': reportType,
        'month': month ?? DateTime.now().month,
        'year': year ?? DateTime.now().year,
        'user_id': userId,
      });

      final response = await _dio.post(
        '/user/api/index.php/api/traffic',
        data: {'payload': encrypted},
      );

      if (response.statusCode == 200) {
        return response.data as Map<String, dynamic>;
      } else {
        throw Exception('فشل في جلب بيانات الاستهلاك');
      }
    } on DioException catch (e) {
      print('[API Error] Get traffic failed: ${e.message}');
      throw Exception('فشل في جلب بيانات الاستهلاك: ${e.message}');
    }
  }

  Future<Map<String, dynamic>> changeService(String newServiceId, String currentPassword) async {
    try {
      final encrypted = AesService().encryptData({
        'new_service': newServiceId,
        'current_password': currentPassword,
      });

      final response = await _dio.post(
        '/user/api/index.php/api/service',
        data: {'payload': encrypted},
      );

      if (response.statusCode == 200) {
        return response.data as Map<String, dynamic>;
      } else {
        throw Exception('فشل في تغيير الخدمة');
      }
    } on DioException catch (e) {
      print('[API Error] Change service failed: ${e.message}');
      throw Exception('فشل في تغيير الخدمة: ${e.message}');
    }
  }

  Future<Map<String, dynamic>> redeemCode(String pin) async {
    try {
      final encrypted = AesService().encryptData({
        'pin': pin,
      });

      final response = await _dio.post(
        '/user/api/index.php/api/redeem',
        data: {'payload': encrypted},
      );

      if (response.statusCode == 200) {
        return response.data as Map<String, dynamic>;
      } else {
        throw Exception('فشل في استخدام الكود');
      }
    } on DioException catch (e) {
      print('[API Error] Redeem code failed: ${e.message}');
      throw Exception('فشل في استخدام الكود: ${e.message}');
    }
  }
}