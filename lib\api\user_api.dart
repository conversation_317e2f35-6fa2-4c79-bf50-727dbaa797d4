import 'dart:convert';
import 'package:http/http.dart' as http;
import '../encryption/aes_service.dart';

class UserApi {
  final String baseUrl;
  UserApi(this.baseUrl);

  Future<Map<String, dynamic>> loginUser(String username, String password, {String language = 'ar'}) async {
    final encrypted = AesService().encryptData({
      'username': username,
      'password': password,
      'language': language
    });
    final response = await http.post(
      Uri.parse('\$baseUrl/auth/login'),
      body: {'payload': encrypted},
    );
    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('فشل تسجيل الدخول');
    }
  }

  Future<Map<String, dynamic>> registerUser(String username, String password, {String language = 'ar'}) async {
    final encrypted = AesService().encryptData({
      'username': username,
      'password': password,
      'language': language
    });
    final response = await http.post(
      Uri.parse('\$baseUrl/register'),
      body: {'payload': encrypted},
    );
    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('فشل تسجيل المستخدم');
    }
  }
}