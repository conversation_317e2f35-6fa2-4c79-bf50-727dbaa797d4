import 'package:dio/dio.dart';
import '../encryption/aes_service.dart';

class UserApi {
  final String baseUrl;
  late final Dio _dio;
  String? _token;

  UserApi(this.baseUrl) {
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'User-Agent': 'ISP-App/1.0',
      },
      validateStatus: (status) {
        return status != null && status < 500;
      },
    ));

    // Add interceptor for logging
    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      requestHeader: true,
      responseHeader: true,
      error: true,
      logPrint: (obj) => print('[API] $obj'),
    ));

    // Add interceptor for token
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        if (_token != null) {
          options.headers['Authorization'] = 'Bearer $_token';
        }
        print('[API] Request to: ${options.uri}');
        print('[API] Request headers: ${options.headers}');
        handler.next(options);
      },
      onResponse: (response, handler) {
        print('[API] Response from: ${response.requestOptions.uri}');
        print('[API] Response status: ${response.statusCode}');
        handler.next(response);
      },
      onError: (error, handler) {
        print('[API] Error from: ${error.requestOptions.uri}');
        print('[API] Error: ${error.message}');
        handler.next(error);
      },
    ));
  }

  void setToken(String token) {
    _token = token;
  }

  Future<Map<String, dynamic>> loginUser(String username, String password, {String language = 'ar'}) async {
    try {
      print('[API] Attempting login for user: $username');

      final loginData = {
        'username': username,
        'password': password,
        'language': language
      };

      print('[API] Login data: $loginData');

      // List of endpoints to try
      final endpoints = [
        '/user/api/index.php/api/auth/login',
        '/user/api/auth/login',
        '/api/auth/login',
        '/auth/login',
        '/user/api/index.php/login',
        '/user/login',
        '/login',
      ];

      // List of content types to try
      final contentTypes = [
        'application/x-www-form-urlencoded',
        'application/json',
        'multipart/form-data',
      ];

      for (String endpoint in endpoints) {
        for (String contentType in contentTypes) {
          try {
            print('[API] Trying endpoint: $endpoint with content-type: $contentType');

            // Configure headers for this attempt
            final options = Options(
              headers: {
                'Content-Type': contentType,
                'Accept': 'application/json',
              },
            );

            dynamic requestData;
            if (contentType == 'application/json') {
              requestData = loginData;
            } else if (contentType == 'application/x-www-form-urlencoded') {
              // Try both encrypted and direct
              try {
                final encrypted = AesService().encryptData(loginData);
                requestData = {'payload': encrypted};
                print('[API] Using encrypted payload');
              } catch (e) {
                requestData = loginData;
                print('[API] Using direct data (encryption failed)');
              }
            } else {
              requestData = FormData.fromMap(loginData);
            }

            final response = await _dio.post(
              endpoint,
              data: requestData,
              options: options,
            );

            print('[API] Success! Endpoint: $endpoint, Status: ${response.statusCode}');
            print('[API] Response data: ${response.data}');

            if (response.statusCode == 200 && response.data != null) {
              final data = response.data is String
                  ? {'message': response.data, 'success': true}
                  : response.data as Map<String, dynamic>;

              // Look for token in different possible fields
              String? token;
              if (data['token'] != null) {
                token = data['token'];
              } else if (data['access_token'] != null) {
                token = data['access_token'];
              } else if (data['auth_token'] != null) {
                token = data['auth_token'];
              }

              if (token != null) {
                setToken(token);
                print('[API] Token found and set: $token');
              }

              return data;
            }

          } catch (e) {
            print('[API] Failed endpoint $endpoint with $contentType: $e');
            continue;
          }
        }
      }

      throw Exception('فشل تسجيل الدخول - جميع endpoints فشلت');

    } on DioException catch (e) {
      print('[API Error] Login failed: ${e.message}');
      print('[API Error] Request: ${e.requestOptions.uri}');
      print('[API Error] Request data: ${e.requestOptions.data}');

      if (e.response != null) {
        print('[API Error] Response status: ${e.response?.statusCode}');
        print('[API Error] Response data: ${e.response?.data}');
        print('[API Error] Response headers: ${e.response?.headers}');
      }

      String errorMessage = 'فشل تسجيل الدخول';
      if (e.type == DioExceptionType.connectionTimeout) {
        errorMessage = 'انتهت مهلة الاتصال';
      } else if (e.type == DioExceptionType.connectionError) {
        errorMessage = 'خطأ في الاتصال بالخادم';
      } else if (e.response?.statusCode == 401) {
        errorMessage = 'اسم المستخدم أو كلمة المرور غير صحيحة';
      } else if (e.response?.statusCode == 404) {
        errorMessage = 'الخدمة غير متوفرة';
      } else if (e.response?.statusCode == 405) {
        errorMessage = 'طريقة الطلب غير مدعومة';
      } else if (e.response?.statusCode == 500) {
        errorMessage = 'خطأ في الخادم';
      }

      throw Exception(errorMessage);
    } catch (e) {
      print('[API Error] Unexpected error: $e');
      throw Exception('فشل تسجيل الدخول: خطأ غير متوقع');
    }
  }

  Future<Map<String, dynamic>> getUserDetails() async {
    try {
      final response = await _dio.get('/user/api/index.php/api/user');

      if (response.statusCode == 200) {
        return response.data as Map<String, dynamic>;
      } else {
        throw Exception('فشل في جلب بيانات المستخدم');
      }
    } on DioException catch (e) {
      print('[API Error] Get user details failed: ${e.message}');
      throw Exception('فشل في جلب بيانات المستخدم: ${e.message}');
    }
  }

  Future<List<dynamic>> getPackages() async {
    try {
      final response = await _dio.get('/user/api/index.php/api/packages');

      if (response.statusCode == 200) {
        return response.data as List<dynamic>;
      } else {
        throw Exception('فشل في جلب الباقات');
      }
    } on DioException catch (e) {
      print('[API Error] Get packages failed: ${e.message}');
      throw Exception('فشل في جلب الباقات: ${e.message}');
    }
  }

  Future<List<dynamic>> getServices() async {
    try {
      final response = await _dio.get('/user/api/index.php/api/service');

      if (response.statusCode == 200) {
        return response.data as List<dynamic>;
      } else {
        throw Exception('فشل في جلب الخدمات');
      }
    } on DioException catch (e) {
      print('[API Error] Get services failed: ${e.message}');
      throw Exception('فشل في جلب الخدمات: ${e.message}');
    }
  }

  Future<Map<String, dynamic>> getInvoices({int page = 1, int count = 10}) async {
    try {
      final encrypted = AesService().encryptData({
        'page': page,
        'count': count,
        'sortBy': 'id',
        'direction': 'desc'
      });

      final response = await _dio.post(
        '/user/api/index.php/api/index/invoice',
        data: {'payload': encrypted},
      );

      if (response.statusCode == 200) {
        return response.data as Map<String, dynamic>;
      } else {
        throw Exception('فشل في جلب الفواتير');
      }
    } on DioException catch (e) {
      print('[API Error] Get invoices failed: ${e.message}');
      throw Exception('فشل في جلب الفواتير: ${e.message}');
    }
  }

  Future<Map<String, dynamic>> getUserSessions({int page = 1, int count = 10}) async {
    try {
      final encrypted = AesService().encryptData({
        'page': page,
        'count': count,
        'sortBy': 'radacctid',
        'direction': 'desc'
      });

      final response = await _dio.post(
        '/user/api/index.php/api/index/session',
        data: {'payload': encrypted},
      );

      if (response.statusCode == 200) {
        return response.data as Map<String, dynamic>;
      } else {
        throw Exception('فشل في جلب الجلسات');
      }
    } on DioException catch (e) {
      print('[API Error] Get sessions failed: ${e.message}');
      throw Exception('فشل في جلب الجلسات: ${e.message}');
    }
  }

  Future<Map<String, dynamic>> getTrafficData({
    String reportType = 'daily',
    int? month,
    int? year,
    int? userId,
  }) async {
    try {
      final encrypted = AesService().encryptData({
        'report_type': reportType,
        'month': month ?? DateTime.now().month,
        'year': year ?? DateTime.now().year,
        'user_id': userId,
      });

      final response = await _dio.post(
        '/user/api/index.php/api/traffic',
        data: {'payload': encrypted},
      );

      if (response.statusCode == 200) {
        return response.data as Map<String, dynamic>;
      } else {
        throw Exception('فشل في جلب بيانات الاستهلاك');
      }
    } on DioException catch (e) {
      print('[API Error] Get traffic failed: ${e.message}');
      throw Exception('فشل في جلب بيانات الاستهلاك: ${e.message}');
    }
  }

  Future<Map<String, dynamic>> changeService(String newServiceId, String currentPassword) async {
    try {
      final encrypted = AesService().encryptData({
        'new_service': newServiceId,
        'current_password': currentPassword,
      });

      final response = await _dio.post(
        '/user/api/index.php/api/service',
        data: {'payload': encrypted},
      );

      if (response.statusCode == 200) {
        return response.data as Map<String, dynamic>;
      } else {
        throw Exception('فشل في تغيير الخدمة');
      }
    } on DioException catch (e) {
      print('[API Error] Change service failed: ${e.message}');
      throw Exception('فشل في تغيير الخدمة: ${e.message}');
    }
  }

  Future<Map<String, dynamic>> redeemCode(String pin) async {
    try {
      final encrypted = AesService().encryptData({
        'pin': pin,
      });

      final response = await _dio.post(
        '/user/api/index.php/api/redeem',
        data: {'payload': encrypted},
      );

      if (response.statusCode == 200) {
        return response.data as Map<String, dynamic>;
      } else {
        throw Exception('فشل في استخدام الكود');
      }
    } on DioException catch (e) {
      print('[API Error] Redeem code failed: ${e.message}');
      throw Exception('فشل في استخدام الكود: ${e.message}');
    }
  }
}