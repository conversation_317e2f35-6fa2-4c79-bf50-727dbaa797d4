import 'package:flutter/material.dart';
import '../services/api_service.dart';
import '../models/package_model.dart';

class PackagesProvider with ChangeNotifier {
  final ApiService _apiService;
  
  List<PackageModel> _packages = [];
  List<PackageModel> _services = [];
  bool _isLoading = false;
  String? _error;

  PackagesProvider(this._apiService);

  // Getters
  List<PackageModel> get packages => _packages;
  List<PackageModel> get services => _services;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Get available packages
  Future<void> loadPackages() async {
    _setLoading(true);
    _clearError();
    
    try {
      _packages = await _apiService.getPackages();
      _clearError();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Get available services
  Future<void> loadServices() async {
    _setLoading(true);
    _clearError();
    
    try {
      _services = await _apiService.getServices();
      _clearError();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Load both packages and services
  Future<void> loadAll() async {
    await Future.wait([
      loadPackages(),
      loadServices(),
    ]);
  }

  // Change user service
  Future<bool> changeService(String newServiceId, String currentPassword) async {
    _setLoading(true);
    _clearError();
    
    try {
      final success = await _apiService.changeService(newServiceId, currentPassword);
      if (success) {
        _clearError();
        // Reload services to get updated data
        await loadServices();
      }
      return success;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Get package by ID
  PackageModel? getPackageById(int id) {
    try {
      return _packages.firstWhere((package) => package.id == id);
    } catch (e) {
      return null;
    }
  }

  // Get service by ID
  PackageModel? getServiceById(int id) {
    try {
      return _services.firstWhere((service) => service.id == id);
    } catch (e) {
      return null;
    }
  }

  // Get popular packages
  List<PackageModel> get popularPackages {
    return _packages.where((package) => package.isPopular).toList();
  }

  // Get active packages
  List<PackageModel> get activePackages {
    return _packages.where((package) => package.isActive).toList();
  }

  // Get packages sorted by price
  List<PackageModel> get packagesSortedByPrice {
    final sortedPackages = List<PackageModel>.from(_packages);
    sortedPackages.sort((a, b) => a.price.compareTo(b.price));
    return sortedPackages;
  }

  // Private methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  // Clear all data
  void clear() {
    _packages.clear();
    _services.clear();
    _clearError();
    notifyListeners();
  }
}
