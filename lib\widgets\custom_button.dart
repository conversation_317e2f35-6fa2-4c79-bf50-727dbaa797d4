import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../utils/app_colors.dart';

enum ButtonType { primary, secondary, outline, text, gradient }

class CustomButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final ButtonType type;
  final IconData? icon;
  final bool isLoading;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final BorderRadius? borderRadius;
  final Gradient? gradient;
  final Color? backgroundColor;
  final Color? textColor;
  final double fontSize;
  final FontWeight fontWeight;

  const CustomButton({
    Key? key,
    required this.text,
    this.onPressed,
    this.type = ButtonType.primary,
    this.icon,
    this.isLoading = false,
    this.width,
    this.height,
    this.padding,
    this.borderRadius,
    this.gradient,
    this.backgroundColor,
    this.textColor,
    this.fontSize = 16,
    this.fontWeight = FontWeight.w600,
  }) : super(key: key);

  @override
  State<CustomButton> createState() => _CustomButtonState();
}

class _CustomButtonState extends State<CustomButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    if (widget.onPressed != null && !widget.isLoading) {
      _controller.forward();
    }
  }

  void _onTapUp(TapUpDetails details) {
    _controller.reverse();
    if (widget.onPressed != null && !widget.isLoading) {
      widget.onPressed!();
    }
  }

  void _onTapCancel() {
    _controller.reverse();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return GestureDetector(
      onTapDown: _onTapDown,
      onTapUp: _onTapUp,
      onTapCancel: _onTapCancel,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: _buildButton(context, isDark),
          );
        },
      ),
    );
  }

  Widget _buildButton(BuildContext context, bool isDark) {
    switch (widget.type) {
      case ButtonType.primary:
        return _buildPrimaryButton(isDark);
      case ButtonType.secondary:
        return _buildSecondaryButton(isDark);
      case ButtonType.outline:
        return _buildOutlineButton(isDark);
      case ButtonType.text:
        return _buildTextButton(isDark);
      case ButtonType.gradient:
        return _buildGradientButton();
    }
  }

  Widget _buildPrimaryButton(bool isDark) {
    return Container(
      width: widget.width,
      height: widget.height ?? 56,
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? 
            (isDark ? AppColors.primaryCyan : AppColors.primaryBlue),
        borderRadius: widget.borderRadius ?? BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: (widget.backgroundColor ?? 
                (isDark ? AppColors.primaryCyan : AppColors.primaryBlue))
                .withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: _buildButtonContent(
        widget.textColor ?? Colors.white,
      ),
    );
  }

  Widget _buildSecondaryButton(bool isDark) {
    return Container(
      width: widget.width,
      height: widget.height ?? 56,
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? 
            (isDark ? AppColors.darkCard : AppColors.neutral100),
        borderRadius: widget.borderRadius ?? BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: _buildButtonContent(
        widget.textColor ?? 
            (isDark ? AppColors.textDarkPrimary : AppColors.textPrimary),
      ),
    );
  }

  Widget _buildOutlineButton(bool isDark) {
    return Container(
      width: widget.width,
      height: widget.height ?? 56,
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: widget.borderRadius ?? BorderRadius.circular(12),
        border: Border.all(
          color: widget.backgroundColor ?? 
              (isDark ? AppColors.primaryCyan : AppColors.primaryBlue),
          width: 2,
        ),
      ),
      child: _buildButtonContent(
        widget.textColor ?? 
            (isDark ? AppColors.primaryCyan : AppColors.primaryBlue),
      ),
    );
  }

  Widget _buildTextButton(bool isDark) {
    return Container(
      width: widget.width,
      height: widget.height ?? 48,
      child: _buildButtonContent(
        widget.textColor ?? 
            (isDark ? AppColors.primaryCyan : AppColors.primaryBlue),
      ),
    );
  }

  Widget _buildGradientButton() {
    return Container(
      width: widget.width,
      height: widget.height ?? 56,
      decoration: BoxDecoration(
        gradient: widget.gradient ?? AppColors.primaryGradient,
        borderRadius: widget.borderRadius ?? BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryBlue.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: _buildButtonContent(
        widget.textColor ?? Colors.white,
      ),
    );
  }

  Widget _buildButtonContent(Color textColor) {
    return Container(
      padding: widget.padding ?? 
          const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      child: Center(
        child: widget.isLoading
            ? SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(textColor),
                ),
              )
            : Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (widget.icon != null) ...[
                    Icon(
                      widget.icon,
                      color: textColor,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                  ],
                  Text(
                    widget.text,
                    style: GoogleFonts.alexandria(
                      fontSize: widget.fontSize,
                      fontWeight: widget.fontWeight,
                      color: textColor,
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}

class IconButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? iconColor;
  final double size;
  final double iconSize;
  final BorderRadius? borderRadius;
  final List<BoxShadow>? boxShadow;

  const IconButton({
    Key? key,
    required this.icon,
    this.onPressed,
    this.backgroundColor,
    this.iconColor,
    this.size = 48,
    this.iconSize = 24,
    this.borderRadius,
    this.boxShadow,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: backgroundColor ?? 
              (isDark ? AppColors.darkCard : Colors.white),
          borderRadius: borderRadius ?? BorderRadius.circular(12),
          boxShadow: boxShadow ?? [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Center(
          child: Icon(
            icon,
            size: iconSize,
            color: iconColor ?? 
                (isDark ? AppColors.textDarkPrimary : AppColors.textPrimary),
          ),
        ),
      ),
    );
  }
}
