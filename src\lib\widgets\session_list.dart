import 'package:flutter/material.dart';

class SessionList extends StatelessWidget {
  final List<Map<String, dynamic>> sessions;
  const SessionList({Key? key, required this.sessions}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (sessions.isEmpty) {
      return const Center(child: Text('لا توجد جلسات متاحة'));
    }
    return ListView.builder(
      itemCount: sessions.length,
      itemBuilder: (context, index) {
        final session = sessions[index];
        return ListTile(
          leading: const Icon(Icons.computer),
          title: Text('IP: ${session['ip'] ?? ''}'),
          subtitle: Text('MAC: ${session['mac'] ?? ''}\nمدة الاتصال: ${session['duration'] ?? ''}'),
        );
      },
    );
  }
}