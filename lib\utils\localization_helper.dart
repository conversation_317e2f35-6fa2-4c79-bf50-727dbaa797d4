import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/language_provider.dart';

class LocalizationHelper {
  static String translate(BuildContext context, String key) {
    return Provider.of<LanguageProvider>(context, listen: false).translate(key);
  }

  static bool isRTL(BuildContext context) {
    return Provider.of<LanguageProvider>(context, listen: false).locale.languageCode == 'ar';
  }

  static TextDirection getTextDirection(BuildContext context) {
    return isRTL(context) ? TextDirection.rtl : TextDirection.ltr;
  }

  static EdgeInsetsGeometry getPadding({
    required double start,
    required double end,
    required double top,
    required double bottom,
    required BuildContext context,
  }) {
    return EdgeInsetsDirectional.fromSTEB(start, top, end, bottom);
  }

  static EdgeInsetsGeometry getMargin({
    required double start,
    required double end,
    required double top,
    required double bottom,
    required BuildContext context,
  }) {
    return EdgeInsetsDirectional.fromSTEB(start, top, end, bottom);
  }

  static AlignmentGeometry getAlignment(BuildContext context, bool start) {
    if (isRTL(context)) {
      return start ? Alignment.centerRight : Alignment.centerLeft;
    } else {
      return start ? Alignment.centerLeft : Alignment.centerRight;
    }
  }
}

// Extension method for easier access
extension LocalizationExtension on BuildContext {
  String tr(String key) => LocalizationHelper.translate(this, key);
  bool get isRTL => LocalizationHelper.isRTL(this);
  TextDirection get textDirection => LocalizationHelper.getTextDirection(this);
}
